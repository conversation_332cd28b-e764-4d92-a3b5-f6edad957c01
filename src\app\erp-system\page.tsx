import { Metadata } from 'next'
import { ERPHero } from "./components/erp-hero"
import { ERPModules } from "./components/erp-modules"
import { ERPFeatures } from "./components/erp-features"
import { ImplementationProcess } from "./components/implementation-process"
import { SuccessStories } from "./components/success-stories"
import { ERPCTA } from "./components/erp-cta"

export const metadata: Metadata = {
  title: 'ERPシステム開発 | MetaLink株式会社',
  description: 'MetaLinkのERPシステム開発サービスでは、業務効率化と経営の可視化を実現する統合基幹システムを提供します。生産管理、在庫管理、販売管理など、お客様の業務に最適なERPソリューションを開発します。',
  keywords: 'ERPシステム, 基幹システム開発, 業務効率化, 生産管理システム, 在庫管理システム, 販売管理システム, MetaLink',
  alternates: {
    canonical: 'https://metalinktech.co.jp/erp-system',
  },
  openGraph: {
    title: 'ERPシステム開発 | MetaLink株式会社',
    description: 'MetaLinkのERPシステム開発サービスでは、業務効率化と経営の可視化を実現する統合基幹システムを提供します。',
    url: 'https://metalinktech.co.jp/erp-system',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
    images: [
      {
        url: 'https://metalinktech.co.jp/images/erp-system.svg',
        width: 1200,
        height: 630,
        alt: 'ERPシステム開発',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ERPシステム開発 | MetaLink株式会社',
    description: 'MetaLinkのERPシステム開発サービスでは、業務効率化と経営の可視化を実現する統合基幹システムを提供します。',
    images: ['https://metalinktech.co.jp/images/erp-system.svg'],
  },
  robots: {
    index: true,
    follow: true,
  }
}

const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'ERPシステム開発',
  provider: {
    '@type': 'Organization',
    name: 'MetaLink',
    url: 'https://metalinktech.co.jp',
    logo: 'https://metalinktech.co.jp/images/logo.webp'
  },
  description: 'MetaLinkのERPシステム開発サービスでは、業務効率化と経営の可視化を実現する統合基幹システムを提供します。',
  serviceType: ['ERPシステム', '基幹システム開発', '業務効率化', '生産管理システム', '在庫管理システム', '販売管理システム'],
  areaServed: {
    '@type': 'Country',
    name: 'Japan'
  },
  offers: {
    '@type': 'Offer',
    itemOffered: [
      {
        '@type': 'Service',
        name: '生産管理システム',
        description: '生産計画から製造管理までを統合的に管理するシステム'
      },
      {
        '@type': 'Service',
        name: '在庫管理システム',
        description: '在庫の最適化と効率的な管理を実現するシステム'
      },
      {
        '@type': 'Service',
        name: '販売管理システム',
        description: '受注から出荷、請求までの販売プロセスを管理するシステム'
      }
    ]
  }
}

export default function ERPSystem() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <main className="min-h-screen" role="main" aria-labelledby="erp-title">
        <ERPHero />
        <ERPModules />
        <ERPFeatures />
        <ImplementationProcess />
        <SuccessStories />
        <ERPCTA />
      </main>
    </>
  )
}
