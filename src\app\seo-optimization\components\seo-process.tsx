import { Card, CardContent } from "@/components/ui/card"

const steps = [
  {
    number: "01",
    title: "初期分析",
    description: "現状のSEO状況を詳細に分析し、改善点を洗い出します。",
  },
  {
    number: "02",
    title: "戦略立案",
    description: "分析結果に基づき、最適なSEO戦略を立案します。",
  },
  {
    number: "03",
    title: "オンページ最適化",
    description: "コンテンツ、メタタグ、内部リンクなどの最適化を実施します。",
  },
  {
    number: "04",
    title: "オフページSEO",
    description: "高品質な被リンクの獲得やソーシャルシグナルの強化を行います。",
  },
  {
    number: "05",
    title: "モニタリングと調整",
    description: "効果を継続的に測定し、戦略の微調整を行います。",
  },
]

export function SEOProcess() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">SEO最適化プロセス</h2>
          <p className="text-lg text-gray-600">効果的なSEO改善を実現する5ステップ</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-blue-600 mb-4">{step.number}</div>
                <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

