@echo off
setlocal enabledelayedexpansion

:: 配置信息
set SERVER_USER=ubuntu
set SERVER_IP=**************
set SERVER_PATH=/mywww/websites/metalinksoft
set SSH_PORT=20122
set SSH_KEY=C:\Users\<USER>\.ssh\id_rsa_tc2

:: 设置项目根目录（当前目录的上一级）
set "PROJECT_ROOT=%~dp0.."
set "SRC_DIR=%PROJECT_ROOT%\src"
set "LOCAL_BUILD_PATH=%SRC_DIR%\.next"

:: 颜色输出
set "GREEN=[92m"
set "RED=[91m"
set "NC=[0m"

:: 输出带颜色的信息
call :info "Starting deployment process..."

:: 检查必要的命令
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    call :error "npm is not installed"
    exit /b 1
)

where ssh >nul 2>nul
if %ERRORLEVEL% neq 0 (
    call :error "ssh is not installed"
    exit /b 1
)

where scp >nul 2>nul
if %ERRORLEVEL% neq 0 (
    call :error "scp is not installed"
    exit /b 1
)

:: 检查SSH密钥文件是否存在
if not exist "%SSH_KEY%" (
    call :error "SSH key not found: %SSH_KEY%"
    exit /b 1
)

:: 检查src目录是否存在
if not exist "%SRC_DIR%" (
    call :error "Source directory not found: %SRC_DIR%"
    exit /b 1
)

@REM :: 构建项目
@REM call :info "Building project..."
@REM cd /d "%SRC_DIR%"
@REM call npm run build
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Build failed"
@REM     exit /b 1
@REM )

@REM :: 检查构建目录是否存在
@REM if not exist "%LOCAL_BUILD_PATH%" (
@REM     call :error "Build directory not found"
@REM     exit /b 1
@REM )

:: 创建临时目录
set "TEMP_DIR=%TEMP%\deploy_%RANDOM%"
mkdir "%TEMP_DIR%" 2>nul
call :info "Created temporary directory: %TEMP_DIR%"

@REM :: 复制构建文件，排除 node_modules 和 deploy 目录
@REM robocopy "%LOCAL_BUILD_PATH%" "%TEMP_DIR%\.next" /E /XD "node_modules" "deploy"
@REM if %ERRORLEVEL% GEQ 8 (
@REM     call :error "Failed to copy build files"
@REM     exit /b 1
@REM )

:: 复制其他必要文件
xcopy /E /I /Y "%SRC_DIR%\app" "%TEMP_DIR%\app"
xcopy /E /I /Y "%SRC_DIR%\components" "%TEMP_DIR%\components"
xcopy /E /I /Y "%SRC_DIR%\public" "%TEMP_DIR%\public"
xcopy /E /I /Y "%SRC_DIR%\.env.local" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\package.json" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\next.config.mjs" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\tsconfig.json" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\postcss.config.mjs" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\tailwind.config.ts" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\ecosystem.config.js" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\components.json" "%TEMP_DIR%"
xcopy /E /I /Y "%SRC_DIR%\app\favicon.ico" "%TEMP_DIR%\favicon.ico"

:: 复制部署脚本到临时目录
xcopy /E /I /Y "%~dp0*" "%TEMP_DIR%\deploy"
if %ERRORLEVEL% neq 0 (
    call :error "Failed to copy deployment scripts"
    exit /b 1
)

:: 创建部署包
set "DEPLOY_PACKAGE=deploy_%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.zip"
set "DEPLOY_PACKAGE=%DEPLOY_PACKAGE: =0%"


:: 使用PowerShell创建zip包
call :info "Creating deployment package..."
powershell -command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%DEPLOY_PACKAGE%' -Force"
if %ERRORLEVEL% neq 0 (
    call :error "Failed to create deployment package"
    exit /b 1
)

:: 清理临时目录
rmdir /S /Q "%TEMP_DIR%"

:: 传输到服务器
call :info "Uploading to server..."
scp -i "%SSH_KEY%" -P %SSH_PORT% "%DEPLOY_PACKAGE%" %SERVER_USER%@%SERVER_IP%:/mywww/temp/
if %ERRORLEVEL% neq 0 (
    call :error "Failed to upload to server"
    exit /b 1
)

:: 在服务器上执行部署
call :info "Completed uploaded on server..."

@REM :: 创建备份
@REM ssh -i "%SSH_KEY%" -p %SSH_PORT% %SERVER_USER%@%SERVER_IP% "if [ -d '%SERVER_PATH%' ]; then mv %SERVER_PATH% %SERVER_PATH%_backup_$(date +%%Y%%m%%d_%%H%%M%%S); fi"
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Failed to create backup"
@REM     exit /b 1
@REM )

@REM :: 创建部署目录
@REM ssh -i "%SSH_KEY%" -p %SSH_PORT% %SERVER_USER%@%SERVER_IP% "mkdir -p %SERVER_PATH%"
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Failed to create deployment directory"
@REM     exit /b 1
@REM )

@REM :: 解压文件
@REM ssh -i "%SSH_KEY%" -p %SSH_PORT% %SERVER_USER%@%SERVER_IP% "unzip -o /tmp/%DEPLOY_PACKAGE% -d %SERVER_PATH%"
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Failed to extract files"
@REM     exit /b 1
@REM )

@REM :: 清理临时文件
@REM ssh -i "%SSH_KEY%" -p %SSH_PORT% %SERVER_USER%@%SERVER_IP% "rm /tmp/%DEPLOY_PACKAGE%"
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Failed to clean up temporary files"
@REM     exit /b 1
@REM )

@REM :: 设置权限
@REM ssh -i "%SSH_KEY%" -p %SSH_PORT% %SERVER_USER%@%SERVER_IP% "chown -R %SERVER_USER%:%SERVER_USER% %SERVER_PATH% && chmod -R 755 %SERVER_PATH%"
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Failed to set permissions"
@REM     exit /b 1
@REM )

@REM :: 执行部署脚本
@REM ssh -i "%SSH_KEY%" -p %SSH_PORT% %SERVER_USER%@%SERVER_IP% "cd %SERVER_PATH% && bash deploy/server-deploy.sh"
@REM if %ERRORLEVEL% neq 0 (
@REM     call :error "Failed to execute deployment script"
@REM     exit /b 1
@REM )

:: 清理本地部署包
del "%DEPLOY_PACKAGE%"

call :info "Deployment completed successfully!"
exit /b 0

:info
echo %GREEN%[INFO] %~1%NC%
exit /b 0

:error
echo %RED%[ERROR] %~1%NC%
exit /b 1 