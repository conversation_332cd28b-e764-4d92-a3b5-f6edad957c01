<svg width="800" height="400" viewBox="0 0 800 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="inventoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="systemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="efficiencyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="800" height="400" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse">
      <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="800" height="400" fill="url(#grid)"/>

  <!-- 化学工厂背景装饰 -->
  <g opacity="0.1" fill="#059669">
    <text x="50" y="100" font-size="20" transform="rotate(-15)">🏭</text>
    <text x="700" y="120" font-size="18" transform="rotate(20)">⚗️</text>
    <text x="80" y="350" font-size="16" transform="rotate(-10)">📦</text>
    <text x="680" y="320" font-size="14" transform="rotate(25)">📊</text>
  </g>

  <!-- 在庫管理システム中央展示 -->
  <g transform="translate(400,200)">
    <!-- 主系统框架 -->
    <g filter="url(#shadow)">
      <rect x="-180" y="-80" width="360" height="160" fill="url(#systemGradient)" rx="15"/>
      <rect x="-177" y="-77" width="354" height="154" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>

    <!-- 系统标题 -->
    <text x="0" y="-40" text-anchor="middle" fill="white" font-size="18" font-weight="bold">在庫管理システム</text>
    <text x="0" y="-20" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="12">Inventory Management System</text>

    <!-- 库存数据展示 -->
    <g transform="translate(-120,-10)">
      <rect width="80" height="50" fill="rgba(255,255,255,0.9)" rx="6"/>
      <text x="40" y="15" text-anchor="middle" fill="#059669" font-size="10" font-weight="bold">原材料A</text>
      <text x="40" y="30" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">1,250</text>
      <text x="40" y="42" text-anchor="middle" fill="#64748b" font-size="8">units</text>
    </g>

    <g transform="translate(-20,-10)">
      <rect width="80" height="50" fill="rgba(255,255,255,0.9)" rx="6"/>
      <text x="40" y="15" text-anchor="middle" fill="#f59e0b" font-size="10" font-weight="bold">製品B</text>
      <text x="40" y="30" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">850</text>
      <text x="40" y="42" text-anchor="middle" fill="#64748b" font-size="8">units</text>
    </g>

    <g transform="translate(80,-10)">
      <rect width="80" height="50" fill="rgba(255,255,255,0.9)" rx="6"/>
      <text x="40" y="15" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="bold">化学品C</text>
      <text x="40" y="30" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">2,100</text>
      <text x="40" y="42" text-anchor="middle" fill="#64748b" font-size="8">units</text>
    </g>
  </g>

  <!-- 效率提升指标 -->
  <g transform="translate(400,80)">
    <circle cx="0" cy="0" r="35" fill="url(#efficiencyGradient)" filter="url(#shadow)"/>
    <circle cx="0" cy="0" r="30" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <text x="0" y="-8" text-anchor="middle" fill="white" font-size="16" font-weight="bold">+50%</text>
    <text x="0" y="8" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">効率向上</text>

    <!-- 脉冲动画 -->
    <circle cx="0" cy="0" r="35" fill="none" stroke="url(#efficiencyGradient)" stroke-width="3" opacity="0.6">
      <animate attributeName="r" values="35;45;35" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 流程改善箭头 -->
  <g transform="translate(150,320)">
    <g filter="url(#shadow)">
      <rect width="120" height="40" fill="url(#inventoryGradient)" rx="8"/>
    </g>
    <text x="60" y="18" text-anchor="middle" fill="white" font-size="11" font-weight="bold">改善前</text>
    <text x="60" y="32" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="9">手動管理</text>

    <!-- 箭头 -->
    <path d="M290 340L350 340" stroke="url(#systemGradient)" stroke-width="4" stroke-linecap="round" marker-end="url(#arrowhead)"/>
    <defs>
      <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="url(#systemGradient)"/>
      </marker>
    </defs>

    <!-- 改善后 -->
    <g transform="translate(220,0)">
      <g filter="url(#shadow)">
        <rect width="120" height="40" fill="url(#inventoryGradient)" rx="8"/>
      </g>
      <text x="60" y="18" text-anchor="middle" fill="white" font-size="11" font-weight="bold">改善後</text>
      <text x="60" y="32" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="9">自動化システム</text>
    </g>
  </g>

  <!-- 实时监控指示器 -->
  <g transform="translate(650,150)">
    <rect width="100" height="60" fill="white" rx="8" filter="url(#shadow)"/>
    <text x="50" y="20" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">リアルタイム</text>
    <text x="50" y="35" text-anchor="middle" fill="#64748b" font-size="10">監視中</text>

    <!-- 状态指示灯 -->
    <circle cx="20" cy="45" r="4" fill="#22c55e">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    <text x="30" y="49" fill="#22c55e" font-size="8">稼働中</text>

    <circle cx="60" cy="45" r="4" fill="#3b82f6">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" begin="0.5s" repeatCount="indefinite"/>
    </circle>
    <text x="70" y="49" fill="#3b82f6" font-size="8">更新中</text>
  </g>

  <!-- 成果数据 -->
  <g transform="translate(100,150)">
    <rect width="100" height="80" fill="white" rx="8" filter="url(#shadow)"/>
    <text x="50" y="20" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">導入成果</text>

    <text x="50" y="40" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold">業務効率</text>
    <text x="50" y="55" text-anchor="middle" fill="#059669" font-size="18" font-weight="bold">50%↑</text>
    <text x="50" y="70" text-anchor="middle" fill="#64748b" font-size="8">向上</text>
  </g>
</svg>
