import { Card, CardContent } from "@/components/ui/card"
import { BarChart } from "lucide-react"

const results = [
  {
    metric: "オーガニックトラフィック",
    increase: "150%",
    description: "SEO最適化により、検索からのトラフィックが大幅に増加しました。",
  },
  {
    metric: "キーワードランキング",
    increase: "Top 3",
    description: "主要なキーワードで検索結果の上位表示を達成しました。",
  },
  {
    metric: "コンバージョン率",
    increase: "50%",
    description: "質の高いトラフィックにより、問い合わせや見積もり依頼が増加しました。",
  },
  {
    metric: "ページ滞在時間",
    increase: "2倍",
    description: "コンテンツの最適化により、ユーザーエンゲージメントが向上しました。",
  },
]

export function SEOResults() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">SEO最適化の期待効果</h2>
          <p className="text-lg text-gray-600">化学業界向けSEO最適化により実現可能な効果をご紹介します</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {results.map((result, index) => (
            <Card key={index}>
              <CardContent className="p-6 text-center">
                <BarChart className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold mb-2">{result.metric}</h3>
                <p className="text-3xl font-bold text-blue-600 mb-2">{result.increase}</p>
                <p className="text-sm text-gray-600">{result.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

