import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '利用規約 | MetaLink株式会社',
  description: 'MetaLink株式会社のサービス利用規約です。サービスの利用条件、アカウント管理、禁止事項、サービスの変更・中断などについて定めています。',
  keywords: '利用規約, 利用条件, サービス規約, アカウント管理, 禁止事項, MetaLink',
  openGraph: {
    title: '利用規約 | MetaLink株式会社',
    description: 'MetaLink株式会社のサービス利用規約です。サービスの利用条件について説明しています。',
    url: 'https://metalinktech.co.jp/terms',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '利用規約 | MetaLink株式会社',
    description: 'MetaLink株式会社のサービス利用規約です。サービスの利用条件について説明しています。',
  },
}

export default function TermsPage() {
  const terms = [
    {
      title: "1. サービスの利用",
      content: `本利用規約（以下「本規約」）は、MetaLink株式会社（以下「当社」）が提供するすべてのサービス（以下「本サービス」）の利用条件を定めるものです。
      \n本サービスをご利用いただく際には、本規約に同意いただいたものとみなします。`,
    },
    {
      title: "2. アカウント管理",
      content: `ユーザーは、本サービスの利用にあたり、以下の事項を遵守するものとします：
      \n・正確かつ最新の情報を提供すること
      \n・アカウント情報の機密性を保持すること
      \n・アカウントの不正使用が判明した場合、直ちに当社に通知すること`,
    },
    {
      title: "3. 禁止事項",
      content: `ユーザーは、本サービスの利用にあたり、以下の行為を行ってはならないものとします：
      \n・法令または公序良俗に違反する行為
      \n・当社または第三者の知的財産権を侵害する行為
      \n・当社または第三者の名誉を毀損する行為
      \n・本サービスの運営を妨害する行為
      \n・その他、当社が不適切と判断する行為`,
    },
    {
      title: "4. サービスの変更・中断",
      content: `当社は、以下の場合に本サービスの全部または一部の提供を中断することがあります：
      \n・システムの保守点検または更新を行う場合
      \n・地震、落雷、火災、停電、天災などの不可抗力により、本サービスの提供が困難となった場合
      \n・その他、当社が中断を必要と判断した場合`,
    },
    {
      title: "5. 免責事項",
      content: `当社は、本サービスに関して、以下の事項について一切の責任を負わないものとします：
      \n・ユーザーが本サービスを利用したことにより生じた損害
      \n・ユーザーと第三者との間で生じたトラブル
      \n・天災地変等の不可抗力により生じた損害`,
    },
    {
      title: "6. 規約の変更",
      content: `当社は、必要と判断した場合には、ユーザーに通知することなく本規約を変更することができるものとします。
      \n変更後の利用規約は、当社ウェブサイトに掲載された時点から効力を生じるものとします。`,
    },
  ]

  return (
    <main className="container mx-auto px-4 py-16" role="main" aria-labelledby="terms-title">
      <h1 id="terms-title" className="text-4xl font-bold text-center mb-12">利用規約</h1>
      
      <div className="max-w-3xl mx-auto">
        {terms.map((term, index) => (
          <section key={index} className="mb-8" aria-labelledby={`term-${index}`}>
            <h2 id={`term-${index}`} className="text-2xl font-semibold mb-4">{term.title}</h2>
            <div className="prose max-w-none">
              {term.content.split('\n').map((paragraph, pIndex) => (
                <p key={pIndex} className="mb-2">
                  {paragraph}
                </p>
              ))}
            </div>
          </section>
        ))}
        
        <section className="mt-12" aria-labelledby="contact-info">
          <h2 id="contact-info" className="text-2xl font-semibold mb-4">お問い合わせ</h2>
          <p>
            本規約に関するお問い合わせは、下記までご連絡ください：
          </p>
          <address className="mt-4 not-italic">
            <p>MetaLink株式会社</p>
        
            <p>Email: <EMAIL></p>
          </address>
        </section>
      </div>
    </main>
  )
}
