<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="600" height="600" fill="#f8fafc"/>
  
  <!-- 搜索排名增长曲线 -->
  <g transform="translate(50,50)">
    <!-- 坐标轴 -->
    <line x1="50" y1="450" x2="500" y2="450" stroke="#64748b" stroke-width="2"/>
    <line x1="50" y1="450" x2="50" y2="50" stroke="#64748b" stroke-width="2"/>
    
    <!-- 网格线 -->
    <g stroke="#e2e8f0" stroke-width="1">
      <line x1="50" y1="350" x2="500" y2="350"/>
      <line x1="50" y1="250" x2="500" y2="250"/>
      <line x1="50" y1="150" x2="500" y2="150"/>
    </g>
    
    <!-- 增长曲线 -->
    <path d="M50,400 Q200,380 300,250 T500,100" 
          fill="none" 
          stroke="#3b82f6" 
          stroke-width="3"/>
    
    <!-- 数据点 -->
    <circle cx="50" cy="400" r="5" fill="#3b82f6"/>
    <circle cx="300" cy="250" r="5" fill="#3b82f6"/>
    <circle cx="500" cy="100" r="5" fill="#3b82f6"/>
    
    <!-- 坐标轴标签 -->
    <g fill="#64748b" font-size="12">
      <text x="30" y="450" text-anchor="end">0</text>
      <text x="30" y="350" text-anchor="end">25</text>
      <text x="30" y="250" text-anchor="end">50</text>
      <text x="30" y="150" text-anchor="end">75</text>
      <text x="30" y="50" text-anchor="end">100</text>
    </g>
    
    <!-- 时间轴标签 -->
    <g fill="#64748b" font-size="12">
      <text x="50" y="470" text-anchor="middle">1月</text>
      <text x="275" y="470" text-anchor="middle">6月</text>
      <text x="500" y="470" text-anchor="middle">12月</text>
    </g>
  </g>

  <!-- 搜索引擎图标 -->
  <g transform="translate(400,150)">
    <circle cx="50" cy="50" r="40" fill="#3b82f6"/>
    <path d="M30,50 L70,50 M50,30 L50,70" 
          stroke="white" 
          stroke-width="4"/>
  </g>

  <!-- 标题 -->
  <text x="300" y="80" text-anchor="middle" fill="#1e293b" font-size="24" font-weight="bold">SEO効果分析</text>
</svg>
