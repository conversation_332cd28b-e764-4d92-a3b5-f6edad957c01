import { ArrowRight } from "lucide-react"
import Link from "next/link"

export function Hero() {
  return (
    <section className="pt-24 pb-12 md:pt-32 md:pb-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6" style={{lineHeight: "1.2"}}>
            化工業界の
            <br className="md:hidden" />
            <span className="text-blue-600">在庫管理コスト30%削減</span>
            <br className="md:hidden" />
            <span className="text-green-600">取引効率50%向上</span>を実現
          </h1>
          <p className="text-lg md:text-xl text-gray-600 mb-6">
            化工業界に特化したB2Bプラットフォーム・ERPシステム開発で
            <br className="md:hidden" />
            お客様のビジネスのDX推進をサポートします
          </p>

          {/* 専門性指標 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-2xl mx-auto">
            <div className="text-center p-4 bg-white/80 rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-600 mb-1">10年+</div>
              <div className="text-sm text-gray-600">業界経験</div>
            </div>
            <div className="text-center p-4 bg-white/80 rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-green-600 mb-1">最新技術</div>
              <div className="text-sm text-gray-600">AI・IoT活用</div>
            </div>
            <div className="text-center p-4 bg-white/80 rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-orange-600 mb-1">24/7</div>
              <div className="text-sm text-gray-600">サポート体制</div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="group inline-flex items-center justify-center gap-2 bg-blue-600 text-white px-8 py-4 rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl font-semibold"
            >
              無料相談で業務効率化の可能性を診断
              <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Link>
            <Link
              href="#case-studies"
              className="inline-flex items-center justify-center gap-2 border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg hover:bg-blue-50 transition-colors font-semibold"
            >
              導入事例を見る
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

