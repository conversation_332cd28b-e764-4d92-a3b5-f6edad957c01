import { Metadata } from 'next'
import { Building, Users, Target, Clock } from "lucide-react"
import { Breadcrumb, generateBreadcrumbJsonLd } from "@/components/breadcrumb"

export const metadata: Metadata = {
  title: '会社概要 | MetaLink株式会社 - 化工業界専門のデジタルソリューション',
  description: 'MetaLinkは、化工業界に特化したデジタルソリューションを提供する日本の新興企業です。10年以上の業界経験を活かし、ERPシステム、B2Bプラットフォーム、Webサイト開発、SEO最適化サービスを提供しています。',
  keywords: 'MetaLink, 会社概要, 化工業界, デジタルソリューション, ERPシステム, B2Bプラットフォーム, システム開発, SEO最適化, 大阪, 日本',
  openGraph: {
    title: '会社概要 | MetaLink株式会社 - 化工業界専門のデジタルソリューション',
    description: 'MetaLinkは、化工業界に特化したデジタルソリューションを提供する日本の新興企業です。10年以上の業界経験を活かし、お客様のデジタル変革を支援します。',
    url: 'https://metalinktech.co.jp/about',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '会社概要 | MetaLink株式会社',
    description: 'MetaLinkは、化工業界に特化したデジタルソリューションを提供する日本の新興企業です。',
  },
}

export default function AboutPage() {
  const breadcrumbItems = [
    { label: '会社概要' }
  ]

  const breadcrumbJsonLd = generateBreadcrumbJsonLd(breadcrumbItems)

  // 組織の構造化データ
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "MetaLink株式会社",
    "alternateName": "MetaLink Co., Ltd.",
    "url": "https://metalinktech.co.jp",
    "logo": "https://metalinktech.co.jp/images/logo.png",
    "description": "化工業界に特化したデジタルソリューションを提供する日本の企業。ERPシステム、B2Bプラットフォーム、Webサイト開発、SEO最適化サービスを提供。",
    "foundingDate": "2025-01-01",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "今市2丁目20番3-303号",
      "addressLocality": "大阪市旭区",
      "addressRegion": "大阪府",
      "postalCode": "535-0011",
      "addressCountry": "JP"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["Japanese"]
    },
    "sameAs": [
      "https://metalinktech.co.jp"
    ],
    "knowsAbout": [
      "化工業界",
      "ERPシステム",
      "B2Bプラットフォーム",
      "Webサイト開発",
      "SEO最適化",
      "デジタルマーケティング"
    ],
    "serviceArea": {
      "@type": "Country",
      "name": "Japan"
    }
  }

  const companyInfo = [
    { label: "社名", value: "MetaLink株式会社" },
    { label: "設立", value: "2025年" },
    { label: "資本金", value: "500万円" },
    { label: "所在地", value: "〒535-0011<br/>大阪府大阪市旭区今市2丁目20番3-303号" },
    { label: "代表取締役社長", value: "カン・ホイ" },
    // { label: "従業員数", value: "5名（2025年6月現在）" },
    { label: "事業内容", value: "・化工業界向けB2Bプラットフォームの開発・運営<br/>・化工業界向けERPシステムの開発・販売<br/>・企業向けWebサイト構築・運営<br/>・SEO最適化コンサルティング<br/>・デジタルマーケティング支援<br/>・システム保守・運用サポート"},
    // { label: "対応業界", value: "化学工業、製薬業界、化成品製造、化粧品業界" }
  ]

  const features = [
    {
      icon: Building,
      title: "化工業界専門特化",
      description: "10年以上の化工業界での経験を活かし、業界特有の課題とニーズを深く理解したソリューションを提供します。原料管理、品質管理、規制対応など、化工業界の複雑な要件に対応可能です。",
    },
    {
      icon: Users,
      title: "経験豊富な専門チーム",
      description: "化工業界での実務経験を持つエンジニアとコンサルタントが、お客様の課題を的確に把握し、最適なデジタルソリューションを設計・開発します。",
    },
    {
      icon: Target,
      title: "最新技術の活用",
      description: "AI・IoT・クラウド技術などの最新テクノロジーと化工業界のベストプラクティスを組み合わせ、効率的で革新的なシステムを構築します。",
    },
    {
      icon: Clock,
      title: "継続的なサポート",
      description: "システム導入後も継続的な保守・運用サポートを提供し、お客様のビジネスの成長と変化に合わせてシステムを最適化し続けます。",
    },
  ]

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
      />
      <div className="pt-20">
        <Breadcrumb items={breadcrumbItems} />
        <div className="container mx-auto px-4 py-16" role="main" aria-labelledby="page-title">
      <h1 id="page-title" className="text-4xl font-bold text-center mb-12 mt-10">会社概要</h1>
      
      <section aria-labelledby="company-info-title" className="mb-16">
        <h2 id="company-info-title" className="text-3xl font-bold mb-8">企業情報</h2>
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {companyInfo.map((info, index) => (
            <div key={index} className="border p-4 rounded">
              <dt className="font-semibold">{info.label}</dt>
              <dd className="pt-2" dangerouslySetInnerHTML={{ __html: info.value }} />
            </div>
          ))}
        </dl>
      </section>

      <section aria-labelledby="mission-title" className="mb-16">
        <h2 id="mission-title" className="text-3xl font-bold mb-8">企業理念</h2>
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-8 rounded-lg">
          <h3 className="text-2xl font-semibold mb-4 text-blue-900">化工業界のデジタル変革を支援し、持続可能な未来を創造する</h3>
          <p className="text-gray-700 leading-relaxed mb-4">
            私たちMetaLinkは、化工業界が直面する複雑な課題に対して、最新のデジタル技術と深い業界知識を組み合わせたソリューションを提供します。
            効率性の向上、品質管理の強化、規制遵守の自動化を通じて、お客様のビジネスの持続的な成長を支援することが私たちの使命です。
          </p>
          <p className="text-gray-700 leading-relaxed">
            日本の化工業界の競争力向上と、より安全で効率的な産業エコシステムの構築に貢献し、
            次世代に向けた持続可能な化学産業の発展を目指します。
          </p>
        </div>
      </section>

      <section aria-labelledby="features-title" className="mb-16">
        <h2 id="features-title" className="text-3xl font-bold mb-8">私たちの強み</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <div key={index} className="flex items-start gap-4 p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow" role="article">
                <div className="p-3 bg-blue-600 rounded-lg flex-shrink-0">
                  <Icon className="w-6 h-6 text-white" aria-hidden="true" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-900">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            )
          })}
        </div>
      </section>
        </div>
      </div>
    </>
  )
}
