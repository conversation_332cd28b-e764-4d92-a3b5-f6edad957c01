import { Card, CardContent } from "@/components/ui/card"

const steps = [
  {
    number: "01",
    title: "要件定義",
    description: "お客様のニーズと目的を詳細にヒアリングし、最適なウェブサイト構成を提案します。",
  },
  {
    number: "02",
    title: "デザイン",
    description: "化学業界の特性を考慮したプロフェッショナルなデザインを作成します。",
  },
  {
    number: "03",
    title: "開発",
    description: "最新の技術を用いて、高性能で安全なウェブサイトを開発します。",
  },
  {
    number: "04",
    title: "テストと最適化",
    description: "品質検証とパフォーマンス最適化を行い、高品質なサイトを実現します。",
  },
  {
    number: "05",
    title: "公開とサポート",
    description: "ウェブサイトの公開後も、継続的な改善とサポートを提供します。",
  },
]

export function DevelopmentProcess() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">開発プロセス</h2>
          <p className="text-lg text-gray-600">高品質なウェブサイトを実現する5ステップ</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-blue-600 mb-4">{step.number}</div>
                <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

