import { Button } from "@/components/ui/button"
import { ArrowRight, CheckCircle2 } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export function PlatformHero() {
  const benefits = ["取引先とのスムーズなコミュニケーション", "業界特化型の取引プラットフォーム", "セキュアな取引環境"]

  return (
    <section className="pt-24 pb-12 md:pt-32 md:pb-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              化工業界に特化した
              <br />
              B2Bプラットフォーム
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              取引先の開拓から商談、取引まで。
              <br />
              業界に特化した機能で、ビジネスプロセスを効率化します。
            </p>
            <ul className="space-y-4 mb-8">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle2 className="text-blue-600 w-5 h-5" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
            <div className="flex gap-4">
              <Link href="/contact">
                <Button size="lg" className="gap-2">
                  無料デモを予約
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
              <Button size="lg" variant="outline">
                資料ダウンロード
              </Button>
            </div>
          </div>
          <div className="relative aspect-video md:aspect-square">
            <Image
              src="/images/hero-platform.svg"
              alt="B2Bプラットフォームのダッシュボード"
              fill
              className="object-cover rounded-lg shadow-2xl"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  )
}
