'use client';

import { useState, useRef } from 'react';
import { Mail, MapPin, Loader2, CheckCircle, XCircle } from "lucide-react";
import { Breadcrumb, generateBreadcrumbJsonLd } from "@/components/breadcrumb";

export default function ContactPage() {
  const breadcrumbItems = [
    { label: 'お問い合わせ' }
  ]

  const breadcrumbJsonLd = generateBreadcrumbJsonLd(breadcrumbItems)

  const contactMethods = [
    {
      icon: Mail,
      title: "メール",
      description: "24時間受付",
      value: <a href="mailto:<EMAIL>"><EMAIL></a>,
    },
    {
      icon: MapPin,
      title: "所在地",
      description: "本社",
      value: "〒535-0011 大阪市旭区今市2丁目20番3-303号",
    },
  ]

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  
  const formRef = useRef<HTMLFormElement>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    const formData = new FormData(e.currentTarget);
    const formValues = {
      name: formData.get('name'),
      email: formData.get('email'),
      message: formData.get('message'),
    };

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formValues),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '送信に失敗しました');
      }

      setSubmitStatus({
        success: true,
        message: data.message || 'お問い合わせありがとうございます。メールの送信が完了しました。'
      });
      // フォームをリセット
      if (formRef.current) {
        formRef.current.reset();
      }
    } catch (error) {
      setSubmitStatus({
        success: false,
        message: error instanceof Error ? error.message : '送信中にエラーが発生しました。しばらくしてからもう一度お試しください。'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
      />
      <div className="pt-20">
        <Breadcrumb items={breadcrumbItems} />
        <main role="main" aria-labelledby="page-title">
      <section className="bg-gradient-to-b from-slate-50 to-white py-20" aria-labelledby="contact-hero">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 id="page-title" className="text-4xl font-bold mb-6">お問い合わせ</h1>
            <p className="text-lg text-slate-600">
              ご質問やご相談がございましたら、<br />
              お気軽にお問い合わせください。
            </p>
          </div>
        </div>
      </section>

      <section className="py-20 bg-slate-50" aria-labelledby="contact-form">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <h2 id="contact-form" className="text-3xl font-bold text-center mb-8">お問い合わせフォーム</h2>
            {submitStatus ? (
              <div className={`p-6 rounded-lg ${submitStatus.success ? 'bg-green-50' : 'bg-red-50'} text-center`}>
                <div className="flex flex-col items-center justify-center space-y-2">
                  {submitStatus.success ? (
                    <CheckCircle className="w-12 h-12 text-green-500" />
                  ) : (
                    <XCircle className="w-12 h-12 text-red-500" />
                  )}
                  <p className={`text-lg font-medium ${submitStatus.success ? 'text-green-800' : 'text-red-800'}`}>
                    {submitStatus.success ? '送信完了' : 'エラー'}
                  </p>
                  <p className="text-sm text-gray-600">{submitStatus.message}</p>
                  {!submitStatus.success && (
                    <button
                      type="button"
                      onClick={() => setSubmitStatus(null)}
                      className="mt-4 px-4 py-2 text-sm text-white bg-primary rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      戻る
                    </button>
                  )}
                </div>
              </div>
            ) : (
              <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium mb-2">
                      お名前 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      className="w-full px-4 py-2 border rounded-lg"
                      aria-required="true"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2">
                      メールアドレス <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      className="w-full px-4 py-2 border rounded-lg"
                      aria-required="true"
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium mb-2">
                      お問い合わせ内容 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={6}
                      required
                      className="w-full px-4 py-2 border rounded-lg"
                      aria-required="true"
                    />
                  </div>
                </div>
                <div className="text-center">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`flex items-center justify-center gap-2 bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary/90 transition-colors ${
                      isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                    }`}
                    aria-label="お問い合わせを送信"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        送信中...
                      </>
                    ) : (
                      '送信する'
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      </section>

      <section className="py-20" aria-labelledby="contact-methods">
        <div className="container mx-auto px-4">
          <h2 id="contact-methods" className="sr-only">お問い合わせ方法</h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {contactMethods.map((method, index) => (
              <div key={index} className="flex items-start gap-4 p-6 bg-white rounded-lg shadow-sm" role="article">
                <div className="p-2 bg-primary rounded-lg">
                  <method.icon className="w-6 h-6 text-white" aria-hidden="true" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg mb-1">{method.title}</h3>
                  <p className="text-sm text-gray-500 mb-2">{method.description}</p>
                  <div className="text-primary">
                    {method.value}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">アクセスマップ</h2>
            <div className="aspect-w-16 aspect-h-9 w-full overflow-hidden rounded-lg shadow-md">
              <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d204.94005312957086!2d135.55280263602987!3d34.72936468190312!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6000e18b51e48101%3A0x93fffa63d7af3e30!2zTWV0YUxpbmvmoKrlvI_kvJrnpL4!5e0!3m2!1szh-CN!2sjp!4v1750039624661!5m2!1szh-CN!2sjp" 
                width="100%" 
                height="450" 
                style={{ border: 0 }} 
                allowFullScreen 
                loading="lazy" 
                referrerPolicy="no-referrer-when-downgrade"
                className="w-full"
              ></iframe>
            </div>
          </div>
        </div>
      </section>


        </main>
      </div>
    </>
  );
}
