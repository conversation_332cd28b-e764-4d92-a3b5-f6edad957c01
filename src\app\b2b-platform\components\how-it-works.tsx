import { ArrowRight } from "lucide-react"

const steps = [
  {
    number: "01",
    title: "アカウント作成",
    description: "必要な情報を入力し、企業アカウントを作成します。",
  },
  {
    number: "02",
    title: "企業情報の登録",
    description: "取り扱い製品や企業情報を登録し、プロフィールを完成させます。",
  },
  {
    number: "03",
    title: "取引先の検索",
    description: "条件に合う取引先を検索し、コンタクトを取ることができます。",
  },
  {
    number: "04",
    title: "取引開始",
    description: "オンライン上で見積もり、発注、請求書の作成まで完結できます。",
  },
]

export function HowItWorks() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">ご利用の流れ</h2>
          <p className="text-lg text-gray-600">簡単4ステップで取引開始</p>
        </div>
        <div className="max-w-5xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="relative p-6 bg-white rounded-lg shadow-sm">
                <div className="flex items-start gap-4">
                  <span className="text-3xl font-bold text-blue-600">{step.number}</span>
                  <div>
                    <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ArrowRight className="hidden md:block absolute -right-6 top-1/2 transform -translate-y-1/2 text-blue-600 w-6 h-6" />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

