<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="300" height="400" fill="#f8fafc"/>
  
  <!-- 图案 -->
  <g transform="translate(50,100)">
    <!-- 外框 -->
    <rect x="0" y="0" width="200" height="200" 
          fill="none" 
          stroke="#e2e8f0" 
          stroke-width="4"
          rx="20"/>
    
    <!-- 中心图案 -->
    <g transform="translate(40,40)">
      <!-- 抽象形状 -->
      <rect x="0" y="0" width="120" height="20" fill="#e2e8f0" rx="4"/>
      <rect x="0" y="40" width="120" height="20" fill="#e2e8f0" rx="4"/>
      <rect x="0" y="80" width="120" height="20" fill="#e2e8f0" rx="4"/>
      <rect x="0" y="120" width="120" height="20" fill="#e2e8f0" rx="4"/>
    </g>
  </g>
</svg>
