import { Card, CardContent } from "@/components/ui/card"

const steps = [
  {
    number: "01",
    title: "要件定義",
    description: "お客様の業務プロセスを詳細に分析し、最適なシステム構成を提案します。",
  },
  {
    number: "02",
    title: "カスタマイズ・開発",
    description: "化学業界特有のニーズに合わせて、システムをカスタマイズします。",
  },
  {
    number: "03",
    title: "データ移行",
    description: "既存システムからのデータを安全かつ正確に新システムへ移行します。",
  },
  {
    number: "04",
    title: "トレーニング・運用開始",
    description: "従業員向けの研修を実施し、スムーズな運用開始をサポートします。",
  },
]

export function ImplementationProcess() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">導入プロセス</h2>
          <p className="text-lg text-gray-600">スムーズな導入をサポートする4ステップ</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-blue-600 mb-4">{step.number}</div>
                <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

