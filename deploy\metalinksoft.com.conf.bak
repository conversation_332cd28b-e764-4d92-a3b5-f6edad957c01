server {
    listen 80;
    server_name metalinksoft.com www.metalinksoft.com;

    location / {
        return 301 https://$server_name$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name metalinksoft.com www.metalinksoft.com;

    if ($host != 'metalinksoft.com') {
        return 301 https://metalinksoft.com$request_uri;
    }

    # SSL 配置
    ssl_certificate /etc/nginx/certs/metalinksoft.com/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/metalinksoft.com/metalinksoft.com.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # 安全头部
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;


    # ✅ Next.js 构建输出静态资源
    location ^~ /_next/static/ {
        alias /websites/metalinksoft/.next/static/;
        expires 365d;
        access_log off;
        #error_log /var/log/nginx/next_static.error.log debug;
	    add_header Cache-Control "public, max-age=31536000, immutable";
        try_files $uri =404;
    }

    # ✅ public/images/
    location ^~ /images/ {
        alias /websites/metalinksoft/public/images/;
        expires 365d;
        access_log off;
        add_header Cache-Control "public, max-age=31536000, immutable";
        try_files $uri =404;
    }

    # ✅ public/fonts/
    location ^~ /fonts/ {
        alias /websites/metalinksoft/public/fonts/;
        expires 365d;
        access_log off;
        add_header Cache-Control "public, max-age=31536000, immutable";
        try_files $uri =404;
    }

    # ✅ public/static/
    location ^~ /static/ {
        alias /websites/metalinksoft/public/static/;
        expires 365d;
        access_log off;
        add_header Cache-Control "public, max-age=31536000, immutable";
        try_files $uri =404;
    }

    # 🚫 不再使用 catch-all 的正则 location，避免干扰 _next 路径

    # ✅ SSR 代理
    location / {
        proxy_pass http://127.0.0.1:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 日志
    access_log /var/log/nginx/metalinksoft.access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/metalinksoft.error.log warn;
}

