import { Metadata } from 'next'
import { SE<PERSON><PERSON> } from "./components/seo-hero"
import { SEOFeatures } from "./components/seo-features"
import { SEOProcess } from "./components/seo-process"
import { SEOServices } from "./components/seo-services"
import { SEOResults } from "./components/seo-results"
import { SEOCTA } from "./components/seo-cta"

export const metadata: Metadata = {
  title: 'SEO対策・検索エンジン最適化サービス | MetaLink株式会社',
  description: 'MetaLinkのSEO対策サービスでは、検索エンジン最適化の専門知識と実績を活かし、Webサイトの検索順位向上と集客改善を実現します。技術的SEO、コンテンツ最適化、内部・外部対策など、包括的なSEOソリューションを提供します。',
  keywords: 'SEO対策, 検索エンジン最適化, SEOコンサルティング, Webサイト集客, コンテンツ最適化, 検索順位改善, MetaLink',
  alternates: {
    canonical: 'https://metalinktech.co.jp/seo-optimization',
  },
  openGraph: {
    title: 'SEO対策・検索エンジン最適化サービス | MetaLink株式会社',
    description: 'MetaLinkのSEO対策サービスでは、検索エンジン最適化の専門知識と実績を活かし、Webサイトの検索順位向上と集客改善を実現します。',
    url: 'https://metalinktech.co.jp/seo-optimization',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
    images: [
      {
        url: 'https://metalinktech.co.jp/images/hero-seo.svg',
        width: 1200,
        height: 630,
        alt: 'MetaLink SEO対策サービス',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SEO対策・検索エンジン最適化サービス | MetaLink株式会社',
    description: 'MetaLinkのSEO対策サービスでは、検索エンジン最適化の専門知識と実績を活かし、Webサイトの検索順位向上と集客改善を実現します。',
    images: ['https://metalinktech.co.jp/images/hero-seo.svg'],
  },
  robots: {
    index: true,
    follow: true,
  }
}

const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'SEO対策・検索エンジン最適化サービス',
  provider: {
    '@type': 'Organization',
    name: 'MetaLink',
    url: 'https://metalinktech.co.jp',
    logo: 'https://metalinktech.co.jp/images/logo.webp'
  },
  description: 'MetaLinkのSEO対策サービスでは、検索エンジン最適化の専門知識と実績を活かし、Webサイトの検索順位向上と集客改善を実現します。',
  serviceType: ['SEO対策', '検索エンジン最適化', 'SEOコンサルティング', 'Webサイト集客', 'コンテンツ最適化'],
  areaServed: {
    '@type': 'Country',
    name: 'Japan'
  },
  offers: {
    '@type': 'Offer',
    itemOffered: [
      {
        '@type': 'Service',
        name: '技術的SEO対策',
        description: 'サイトの技術的な最適化による検索エンジン対応'
      },
      {
        '@type': 'Service',
        name: 'コンテンツ最適化',
        description: '検索エンジンに最適化されたコンテンツの作成と改善'
      },
      {
        '@type': 'Service',
        name: 'SEOコンサルティング',
        description: '包括的なSEO戦略の立案と実施支援'
      }
    ]
  }
}

export default function SEOOptimization() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <main className="min-h-screen" role="main" aria-labelledby="seo-title">
        <SEOHero />
        <SEOFeatures />
        <SEOProcess />
        <SEOServices />
        <SEOResults />
        <SEOCTA />
      </main>
    </>
  )
}
