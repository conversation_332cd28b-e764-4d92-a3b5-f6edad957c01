import { Button } from "@/components/ui/button"
import { ArrowRight, CheckCircle2 } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export function WebDevHero() {
  const benefits = ["化学業界に特化したデザインとUX", "SEO最適化による高い検索順位", "セキュアで高性能なウェブサイト"]

  return (
    <section className="pt-24 pb-12 md:pt-32 md:pb-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              化工業界向け
              <br />
              専門ウェブサイト開発
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              業界知識を活かした、最適化された
              <br />
              企業サイトやECサイトを構築します。
            </p>
            <ul className="space-y-4 mb-8">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle2 className="text-blue-600 w-5 h-5" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
            <div className="flex gap-4">
              <Link href="/contact">
                <Button size="lg" className="gap-2">
                  無料相談を予約
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
              <Button size="lg" variant="outline">
                ポートフォリオを見る
              </Button>
            </div>
          </div>
          <div className="relative aspect-video md:aspect-square">
            <Image
              src="/images/hero-web.svg"
              alt="化工業界向けウェブサイトのイメージ"
              fill
              className="object-cover rounded-lg shadow-2xl"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  )
}
