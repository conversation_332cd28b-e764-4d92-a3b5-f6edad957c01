"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"

interface FAQItem {
  question: string
  answer: string
}

interface FAQProps {
  items: FAQItem[]
  title?: string
  description?: string
}

export function FAQ({ 
  items, 
  title = "よくあるご質問",
  description = "お客様からよくいただくご質問にお答えします"
}: FAQProps) {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  // FAQ構造化データ生成
  const faqJsonLd = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": items.map(item => ({
      "@type": "Question",
      "name": item.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer
      }
    }))
  }

  return (
    <section className="py-20 bg-white">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqJsonLd) }}
      />
      
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{title}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">{description}</p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {items.map((item, index) => {
              const isOpen = openItems.includes(index)
              return (
                <div key={index} className="border border-gray-200 rounded-lg">
                  <button
                    onClick={() => toggleItem(index)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                    aria-expanded={isOpen}
                    aria-controls={`faq-answer-${index}`}
                  >
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {item.question}
                    </h3>
                    {isOpen ? (
                      <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                    )}
                  </button>
                  
                  {isOpen && (
                    <div 
                      id={`faq-answer-${index}`}
                      className="px-6 pb-4"
                    >
                      <div className="text-gray-600 leading-relaxed">
                        {item.answer}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            他にご質問がございましたら、お気軽にお問い合わせください
          </p>
          <a
            href="/contact"
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-8 py-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold shadow-lg hover:shadow-xl"
          >
            お問い合わせ
          </a>
        </div>
      </div>
    </section>
  )
}

// 化工業界専用のFAQ
export const chemicalIndustryFAQ: FAQItem[] = [
  {
    question: "化工業界に特化したシステムの特徴は何ですか？",
    answer: "化工業界特有の在庫管理（ロット管理、有効期限管理）、安全データシート（SDS）管理、法規制対応、品質管理システムなど、業界の専門要件に対応した機能を標準搭載しています。"
  },
  {
    question: "導入期間はどのくらいかかりますか？",
    answer: "プロジェクトの規模により異なりますが、一般的にERPシステムで3-6ヶ月、B2Bプラットフォームで2-4ヶ月、Webサイト開発で1-3ヶ月程度です。事前の要件定義により正確な期間をお見積もりします。"
  },
  {
    question: "既存システムとの連携は可能ですか？",
    answer: "はい、API連携やデータ移行により既存システムとの連携が可能です。会計システム、生産管理システム、販売管理システムなど、様々なシステムとの連携実績があります。"
  },
  {
    question: "サポート体制はどうなっていますか？",
    answer: "24時間365日の技術サポートを提供しています。電話、メール、リモートサポートによる迅速な対応で、システムの安定稼働をサポートします。また、定期的なシステム更新とセキュリティパッチの提供も行っています。"
  },
  {
    question: "費用はどのくらいかかりますか？",
    answer: "プロジェクトの規模や要件により異なります。初期費用とランニングコストを含めた総合的なお見積もりを無料で提供いたします。ROI（投資対効果）を重視した提案を心がけています。"
  },
  {
    question: "セキュリティ対策はどうなっていますか？",
    answer: "厳格な情報セキュリティマネジメント体制により、データの暗号化、多層アクセス制御、定期的なセキュリティ監査を実施しています。化工業界の機密情報を安全に保護し、業界標準のセキュリティ対策を徹底しています。"
  }
]
