<svg width="800" height="400" viewBox="0 0 800 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="seoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="trafficGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="cosmeticGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#db2777;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="800" height="400" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="800" height="400" fill="url(#grid)"/>

  <!-- 化妆品业务背景装饰 -->
  <g opacity="0.1" fill="#ec4899">
    <text x="50" y="100" font-size="18" transform="rotate(-15)">💄</text>
    <text x="720" y="120" font-size="16" transform="rotate(20)">🔍</text>
    <text x="80" y="350" font-size="14" transform="rotate(-10)">📈</text>
    <text x="680" y="320" font-size="12" transform="rotate(25)">✨</text>
  </g>

  <!-- 主要增长图表 -->
  <g transform="translate(400,200)">
    <!-- 背景图表区域 -->
    <rect x="-150" y="-120" width="300" height="200" fill="white" rx="15" filter="url(#shadow)"/>
    <rect x="-145" y="-115" width="290" height="190" fill="none" stroke="rgba(16,185,129,0.2)" stroke-width="2" rx="12"/>

    <!-- 图表标题 -->
    <text x="0" y="-90" text-anchor="middle" fill="#1e293b" font-size="16" font-weight="bold">Organic Traffic 成長</text>

    <!-- Y轴标签 -->
    <text x="-130" y="-60" fill="#64748b" font-size="10">Traffic</text>
    <text x="-130" y="-30" fill="#64748b" font-size="10">200%</text>
    <text x="-130" y="0" fill="#64748b" font-size="10">100%</text>
    <text x="-130" y="30" fill="#64748b" font-size="10">50%</text>
    <text x="-130" y="60" fill="#64748b" font-size="10">0%</text>

    <!-- 网格线 -->
    <g stroke="#e2e8f0" stroke-width="1" opacity="0.5">
      <line x1="-120" y1="-60" x2="120" y2="-60"/>
      <line x1="-120" y1="-30" x2="120" y2="-30"/>
      <line x1="-120" y1="0" x2="120" y2="0"/>
      <line x1="-120" y1="30" x2="120" y2="30"/>
      <line x1="-120" y1="60" x2="120" y2="60"/>
    </g>

    <!-- 增长曲线 -->
    <path d="M-120,60 Q-60,45 -20,30 Q20,15 60,0 Q100,-15 120,-30"
          stroke="url(#seoGradient)"
          stroke-width="4"
          fill="none"
          stroke-linecap="round"
          filter="url(#glow)"/>

    <!-- 数据点 -->
    <circle cx="-120" cy="60" r="4" fill="url(#seoGradient)"/>
    <circle cx="-60" cy="45" r="4" fill="url(#seoGradient)"/>
    <circle cx="-20" cy="30" r="4" fill="url(#seoGradient)"/>
    <circle cx="20" cy="15" r="4" fill="url(#seoGradient)"/>
    <circle cx="60" cy="0" r="4" fill="url(#seoGradient)"/>
    <circle cx="120" cy="-30" r="6" fill="url(#seoGradient)">
      <animate attributeName="r" values="6;10;6" dur="2s" repeatCount="indefinite"/>
    </circle>

    <!-- X轴标签 -->
    <text x="-120" y="80" text-anchor="middle" fill="#64748b" font-size="9">1月</text>
    <text x="-60" y="80" text-anchor="middle" fill="#64748b" font-size="9">2月</text>
    <text x="-20" y="80" text-anchor="middle" fill="#64748b" font-size="9">3月</text>
    <text x="20" y="80" text-anchor="middle" fill="#64748b" font-size="9">4月</text>
    <text x="60" y="80" text-anchor="middle" fill="#64748b" font-size="9">5月</text>
    <text x="120" y="80" text-anchor="middle" fill="#64748b" font-size="9">6月</text>
  </g>

  <!-- 3倍增长指示器 -->
  <g transform="translate(400,80)">
    <circle cx="0" cy="0" r="35" fill="url(#cosmeticGradient)" filter="url(#shadow)"/>
    <circle cx="0" cy="0" r="30" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <text x="0" y="-8" text-anchor="middle" fill="white" font-size="16" font-weight="bold">×3</text>
    <text x="0" y="8" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Traffic増加</text>

    <!-- 脉冲动画 -->
    <circle cx="0" cy="0" r="35" fill="none" stroke="url(#cosmeticGradient)" stroke-width="3" opacity="0.6">
      <animate attributeName="r" values="35;45;35" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- SEO施策展示 -->
  <g transform="translate(150,120)">
    <rect width="120" height="80" fill="white" rx="10" filter="url(#shadow)"/>
    <text x="60" y="20" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">SEO施策</text>

    <rect x="15" y="30" width="90" height="12" fill="url(#seoGradient)" rx="2"/>
    <text x="60" y="39" text-anchor="middle" fill="white" font-size="9" font-weight="bold">キーワード最適化</text>

    <rect x="15" y="48" width="90" height="12" fill="url(#trafficGradient)" rx="2"/>
    <text x="60" y="57" text-anchor="middle" fill="white" font-size="9" font-weight="bold">コンテンツ強化</text>

    <rect x="15" y="66" width="90" height="8" fill="#64748b" rx="2"/>
    <text x="60" y="72" text-anchor="middle" fill="white" font-size="8">技術的改善</text>
  </g>

  <!-- 成果指标 -->
  <g transform="translate(550,120)">
    <rect width="120" height="80" fill="white" rx="10" filter="url(#shadow)"/>
    <text x="60" y="20" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">成果指標</text>

    <text x="60" y="35" text-anchor="middle" fill="#10b981" font-size="10" font-weight="bold">検索順位</text>
    <text x="60" y="48" text-anchor="middle" fill="#10b981" font-size="14" font-weight="bold">1-3位</text>

    <text x="60" y="62" text-anchor="middle" fill="#3b82f6" font-size="10" font-weight="bold">CV率</text>
    <text x="60" y="75" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="bold">+150%</text>
  </g>

  <!-- 化妆品ECサイト表示 -->
  <g transform="translate(100,280)">
    <rect width="200" height="80" fill="url(#cosmeticGradient)" rx="12" filter="url(#shadow)"/>
    <rect x="3" y="3" width="194" height="74" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="9"/>

    <text x="100" y="25" text-anchor="middle" fill="white" font-size="14" font-weight="bold">化粧品ECサイト</text>
    <text x="100" y="42" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="11">Cosmetics E-commerce</text>

    <!-- 商品アイコン -->
    <g transform="translate(30,50)">
      <rect width="20" height="15" fill="rgba(255,255,255,0.9)" rx="2"/>
      <text x="10" y="11" text-anchor="middle" fill="#ec4899" font-size="8">💄</text>
    </g>
    <g transform="translate(60,50)">
      <rect width="20" height="15" fill="rgba(255,255,255,0.9)" rx="2"/>
      <text x="10" y="11" text-anchor="middle" fill="#ec4899" font-size="8">✨</text>
    </g>
    <g transform="translate(90,50)">
      <rect width="20" height="15" fill="rgba(255,255,255,0.9)" rx="2"/>
      <text x="10" y="11" text-anchor="middle" fill="#ec4899" font-size="8">🧴</text>
    </g>

    <text x="170" y="60" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">売上向上</text>
  </g>

  <!-- 検索エンジン表示 -->
  <g transform="translate(500,280)">
    <rect width="200" height="80" fill="white" rx="12" filter="url(#shadow)"/>

    <!-- 検索バー -->
    <rect x="20" y="15" width="160" height="20" fill="#f1f5f9" rx="10"/>
    <text x="100" y="28" text-anchor="middle" fill="#64748b" font-size="10">化粧品 おすすめ</text>
    <circle cx="170" cy="25" r="8" fill="url(#seoGradient)"/>
    <text x="170" y="28" text-anchor="middle" fill="white" font-size="8">🔍</text>

    <!-- 検索結果 -->
    <rect x="20" y="45" width="160" height="12" fill="url(#seoGradient)" rx="2"/>
    <text x="25" y="53" fill="white" font-size="8" font-weight="bold">1位: お客様のサイト</text>

    <rect x="20" y="62" width="160" height="8" fill="#e2e8f0" rx="2"/>
    <text x="25" y="68" fill="#64748b" font-size="7">2位: 競合サイト</text>
  </g>

  <!-- 上昇箭头 -->
  <g transform="translate(720,200)" fill="url(#seoGradient)" opacity="0.8">
    <path d="M0,30 L15,0 L30,30 L22,30 L22,50 L8,50 L8,30 Z">
      <animateTransform attributeName="transform" type="translate" values="0,0; 0,-15; 0,0" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
</svg>
