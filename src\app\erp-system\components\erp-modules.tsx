import { FlaskRoundIcon as Flask, Truck, BarChart2, ShieldCheck, Users, DollarSign } from "lucide-react"

const modules = [
  {
    icon: Flask,
    title: "製造管理",
    description: "バッチ生産、連続生産に対応。原料の追跡から製品の品質管理まで一元化。",
  },
  {
    icon: Truck,
    title: "在庫・物流管理",
    description: "リアルタイムの在庫把握と最適な物流ルートの提案で効率的な管理を実現。",
  },
  {
    icon: BarChart2,
    title: "品質管理",
    description: "厳格な品質基準の遵守と、製造プロセスの継続的な改善をサポート。",
  },
  {
    icon: ShieldCheck,
    title: "規制遵守",
    description: "化学物質規制に準拠した管理と、必要書類の自動生成機能を搭載。",
  },
  {
    icon: Users,
    title: "顧客管理",
    description: "取引先情報の一元管理と、営業活動の効率化をサポート。",
  },
  {
    icon: DollarSign,
    title: "財務管理",
    description: "原価計算から財務諸表作成まで、化学業界特有の会計処理に対応。",
  },
]

export function ERPModules() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">化工業界に特化した統合モジュール</h2>
          <p className="text-lg text-gray-600">業界特有のニーズに応える、専門機能を網羅</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {modules.map((module, index) => (
            <div key={index} className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <module.icon className="w-12 h-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-bold mb-3">{module.title}</h3>
              <p className="text-gray-600">{module.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

