import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number(process.env.SMTP_PORT) || 465,
  secure: true,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

export async function POST(request: Request) {
  try {
    const { name, email, message } = await request.json();

    // バリデーション
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'すべての必須項目を入力してください。' },
        { status: 400 }
      );
    }

    // メール送信
    const mailOptions = {
      from: process.env.SMTP_FROM,
      to: process.env.TO_EMAIL || 'info@metalinks oft.com',
      replyTo: email,
      subject: `お問い合わせ: ${name}様より`,
      text: `
        お名前: ${name}
        メールアドレス: ${email}
        
        お問い合わせ内容:
        ${message}
      `,
      html: `
        <h2>お問い合わせがありました</h2>
        <p><strong>お名前:</strong> ${name}</p>
        <p><strong>メールアドレス:</strong> ${email}</p>
        <p><strong>お問い合わせ内容:</strong></p>
        <p>${message.replace(/\n/g, '<br>')}</p>
      `,
    };
 
    await transporter.sendMail(mailOptions);


    return NextResponse.json(
      { message: 'お問い合わせありがとうございます。メールの送信が完了しました。' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'メールの送信中にエラーが発生しました。しばらくしてからもう一度お試しください。' },
      { status: 500 }
    );
  }
}
