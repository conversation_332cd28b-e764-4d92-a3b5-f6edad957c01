import { Palette, Search, ShieldCheck, Zap, Globe, BarChart2 } from "lucide-react"

const features = [
  {
    icon: Palette,
    title: "業界特化デザイン",
    description: "化学業界の特性を理解したプロフェッショナルなデザインを提供します。",
  },
  {
    icon: Search,
    title: "SEO最適化",
    description: "化学関連キーワードに特化したSEO対策で、検索順位の向上を実現します。",
  },
  {
    icon: ShieldCheck,
    title: "セキュリティ対策",
    description: "最新のセキュリティ技術を導入し、安全なウェブ環境を構築します。",
  },
  {
    icon: Zap,
    title: "高速パフォーマンス",
    description: "最適化された코드と最新技術により、高速で快適な閲覧体験を提供します。",
  },
  {
    icon: Globe,
    title: "多言語対応",
    description: "グローバルビジネスに対応した多言語サイトの開発をサポートします。",
  },
  {
    icon: BarChart2,
    title: "アクセス解析",
    description: "詳細なアクセス解析により、ウェブサイトの改善点を可視化します。",
  },
]

export function WebDevFeatures() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">化工業界に特化したウェブ開発</h2>
          <p className="text-lg text-gray-600">業界特有のニーズに応える、専門的な機能とデザイン</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <feature.icon className="w-12 h-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

