import { Button } from "@/components/ui/button"
import { ArrowRight, CheckCircle2 } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export function SEOHero() {
  const benefits = [
    "化学業界に特化したキーワード戦略",
    "オーガニック検索トラフィックの増加",
    "ブランド認知度と信頼性の向上",
  ]

  return (
    <section className="pt-24 pb-12 md:pt-32 md:pb-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              化工業界向け
              <br />
              専門SEO対策
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              業界知識を活かした最適化戦略で、
              <br />
              検索エンジンでの露出を最大化します。
            </p>
            <ul className="space-y-4 mb-8">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle2 className="text-blue-600 w-5 h-5" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
            <div className="flex gap-4">
              <Link href="/contact">
                <Button size="lg" className="gap-2">
                  無料SEO診断を受ける
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
              <Button size="lg" variant="outline">
                サービス詳細
              </Button>
            </div>
          </div>
          <div className="relative aspect-video md:aspect-square">
            <Image
              src="/images/hero-seo.svg?v=0626"
              alt="SEO最適化のイメージ"
              fill
              className="object-cover rounded-lg shadow-2xl"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  )
}
