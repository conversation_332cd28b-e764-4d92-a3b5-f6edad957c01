import { Button } from "@/components/ui/button"
import { ArrowRight, CheckCircle2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export function ERPHero() {
  const benefits = [
    "化工業界特有のプロセスに最適化",
    "リアルタイムデータ分析と可視化",
    "コンプライアンスと品質管理の強化",
  ]

  return (
    <section className="pt-24 pb-12 md:pt-32 md:pb-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              化工業界向け
              <br />
              統合ERPシステム
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              原料調達から製造、品質管理、販売まで。
              <br />
              化学製品のライフサイクル全体を一元管理。
            </p>
            <ul className="space-y-4 mb-8">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle2 className="text-blue-600 w-5 h-5" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
            <div className="flex gap-4">
                <Link href="/contact">
                  <Button size="lg" className="gap-2">
                    デモを依頼
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </Link>
              <Button size="lg" variant="outline">
                資料ダウンロード
              </Button>
            </div>
          </div>
          <div className="relative aspect-video md:aspect-square">
            <Image
              src="/images/erp-system.svg"
              alt="化工ERPシステムのダッシュボード"
              fill
              className="object-cover rounded-lg shadow-2xl"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  )
}
