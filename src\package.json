{"name": "metalinkweb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next": "^14.2.28", "nodemailer": "^7.0.3", "react": "^18", "react-dom": "^18", "resend": "^4.5.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}