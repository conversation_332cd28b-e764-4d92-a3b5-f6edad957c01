"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Menu, X } from "lucide-react"
import { services } from "@/config/services"

export function Header() {
  const [isOpen, setIsOpen] = useState(false)
  const [isServicesOpen, setIsServicesOpen] = useState(false)

  return (
    <header className="fixed top-0 w-full bg-white/90 backdrop-blur-md z-50 border-b border-gray-200 shadow-sm">
      <nav className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link 
            href="/" 
            className="flex items-center space-x-2 hover:opacity-90 transition-opacity"
            aria-label="MetaLink - ホームページへ"
          >
            <Image alt="MetaLink" src="/images/logo0612.svg?v=0626" width={200} height={56} />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <div className="relative group">
              <button
                className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 py-2 font-medium transition-colors duration-200"
                onMouseEnter={() => setIsServicesOpen(true)}
                onMouseLeave={() => setIsServicesOpen(false)}
              >
                <span>サービス</span>
                <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              <div
                className={`absolute left-0 w-64 bg-white rounded-lg shadow-lg border transform transition-all duration-200 ${
                  isServicesOpen 
                    ? "opacity-100 translate-y-0 visible" 
                    : "opacity-0 -translate-y-2 invisible"
                }`}
                onMouseEnter={() => setIsServicesOpen(true)}
                onMouseLeave={() => setIsServicesOpen(false)}
              >
                <div className="py-2">
                  {services.map((service, index) => (
                    <Link
                      key={index}
                      href={service.link}
                      className="flex items-center px-4 py-2 text-sm text-slate-600 hover:bg-slate-50"
                    >
                      <service.icon className="w-5 h-5 mr-3 text-blue-500" />
                      <div>
                        <div className="font-medium text-slate-900">{service.title}</div>
                        <p className="text-xs text-slate-500 mt-0.5">{service.description}</p>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <Link href="/about" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
              会社概要
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
              お問い合わせ
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden text-slate-600 hover:text-slate-900"
          >
            {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden mt-4 pb-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="font-medium px-2">サービス</div>
                {services.map((service, index) => (
                  <Link
                    key={index}
                    href={service.link}
                    className="flex items-center px-2 py-2 text-sm text-slate-600 hover:bg-slate-50 rounded-lg"
                    onClick={() => setIsOpen(false)}
                  >
                    <service.icon className="w-5 h-5 mr-3 text-blue-500" />
                    <div>
                      <div className="font-medium text-slate-900">{service.title}</div>
                      <p className="text-xs text-slate-500 mt-0.5">{service.description}</p>
                    </div>
                  </Link>
                ))}
              </div>
              <Link
                href="/about"
                className="block px-2 py-1 text-slate-600 hover:text-slate-900"
                onClick={() => setIsOpen(false)}
              >
                会社概要
              </Link>
              <Link
                href="/contact"
                className="block px-2 py-1 text-slate-600 hover:text-slate-900"
                onClick={() => setIsOpen(false)}
              >
                お問い合わせ
              </Link>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
