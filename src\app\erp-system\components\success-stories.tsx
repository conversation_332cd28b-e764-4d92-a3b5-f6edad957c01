import { Card, CardContent } from "@/components/ui/card"

const stories = [
  {
    company: "化学工業メーカー",
    position: "生産管理部長",
    content:
      "導入後、生産効率が大幅に向上し、在庫回転率も改善しました。リアルタイムのデータ分析により、迅速で正確な意思決定が可能になりました。",
    results: ["生産効率20%向上", "在庫回転率改善", "意思決定スピード向上"]
  },
  {
    company: "製薬メーカー",
    position: "品質管理部長",
    content:
      "品質管理プロセスが大幅に効率化され、トレーサビリティも向上しました。規制遵守の自動化により、コンプライアンスリスクの大幅な低減を実現しています。",
    results: ["品質管理効率化", "トレーサビリティ向上", "コンプライアンスリスク低減"]
  },
  {
    company: "化成品メーカー",
    position: "経営企画部長",
    content:
      "全社的な業務の可視化により、経営判断のスピードが飛躍的に向上しました。クラウドベースのシステムにより、リモートワークへの対応も容易になりました。",
    results: ["経営判断スピード向上", "業務可視化実現", "リモートワーク対応"]
  },
]

export function SuccessStories() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">導入効果事例</h2>
          <p className="text-lg text-gray-600">ERPシステム導入により実現可能な効果をご紹介します</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stories.map((story, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                    <p className="font-bold text-gray-900">{story.company}</p>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">{story.position}</p>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">{story.content}</p>

                {/* 導入効果 */}
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">導入効果：</h4>
                  <div className="space-y-2">
                    {story.results.map((result, resultIndex) => (
                      <div key={resultIndex} className="flex items-center text-sm">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                        <span className="text-gray-600">{result}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
