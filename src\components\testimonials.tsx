import { Star, Quote } from "lucide-react"

const testimonials = [
  {
    position: "IT部長",
    company: "大手化学メーカー",
    content: "在庫管理システムの導入により、手動作業が大幅に削減され、リアルタイムでの在庫把握が可能になりました。化工業界特有の要件を深く理解した設計で、業務効率が飛躍的に向上しています。",
    rating: 5,
    results: ["作業時間50%削減", "年間コスト大幅削減", "在庫精度99%達成"]
  },
  {
    position: "営業部長",
    company: "中堅化学品商社",
    content: "B2Bプラットフォームの導入で、取引先との連絡業務が劇的に効率化されました。受注処理の自動化により、顧客対応の質も向上し、取引先からの評価も上がっています。",
    rating: 5,
    results: ["受注処理40%短縮", "顧客満足度向上", "業務効率大幅改善"]
  },
  {
    position: "マーケティング部長",
    company: "化粧品メーカー",
    content: "ECサイトのSEO対策とシステム改善により、オーガニックトラフィックが大幅に増加し、売上も前年を大きく上回りました。化工業界の知識と最新技術を組み合わせた提案が非常に効果的でした。",
    rating: 5,
    results: ["トラフィック大幅増", "売上大幅向上", "コンバージョン率改善"]
  }
]

export function Testimonials() {
  return (
    <section className="py-20 bg-gradient-to-b from-slate-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">導入効果事例</h2>
          <p className="text-slate-600 max-w-2xl mx-auto">
            化工業界でのシステム導入により実現可能な効果をご紹介します
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg p-8 relative">
              {/* Quote icon */}
              <div className="absolute top-6 right-6">
                <Quote className="w-8 h-8 text-blue-200" />
              </div>

              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Content */}
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                &ldquo;{testimonial.content}&rdquo;
              </blockquote>

              {/* Results */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">導入効果：</h4>
                <div className="space-y-2">
                  {testimonial.results.map((result, resultIndex) => (
                    <div key={resultIndex} className="flex items-center text-sm">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      <span className="text-gray-600">{result}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Author */}
              <div className="border-t pt-6">
                <div className="text-sm text-gray-600">{testimonial.position}</div>
                <div className="text-sm text-blue-600 font-medium">{testimonial.company}</div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            あなたの会社も成功事例の一つになりませんか？
          </p>
          <a
            href="/contact"
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-8 py-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold shadow-lg hover:shadow-xl"
          >
            無料相談を申し込む
          </a>
        </div>
      </div>
    </section>
  )
}
