import { Users, Building2, ShieldCheck, MessageSquare, BarChart3, Search } from "lucide-react"

const features = [
  {
    icon: Users,
    title: "取引先マッチング",
    description: "AIを活用した高精度なマッチングで、最適な取引先を見つけることができます。",
  },
  {
    icon: Building2,
    title: "企業情報管理",
    description: "取引先の詳細な情報を一元管理し、スムーズな取引を実現します。",
  },
  {
    icon: ShieldCheck,
    title: "セキュリティ対策",
    description: "業界基準に準拠したセキュリティで、安全な取引環境を提供します。",
  },
  {
    icon: MessageSquare,
    title: "商談管理",
    description: "オンライン上で商談から見積もり作成まで、一貫して管理できます。",
  },
  {
    icon: BarChart3,
    title: "取引分析",
    description: "取引データを可視化し、ビジネスの意思決定をサポートします。",
  },
  {
    icon: Search,
    title: "製品検索",
    description: "化工業界特有の専門的な検索機能で、必要な製品を素早く見つけられます。",
  },
]

export function Features() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">充実した機能で取引をサポート</h2>
          <p className="text-lg text-gray-600">業界特有のニーズに応える、専門機能を搭載</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <feature.icon className="w-12 h-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

