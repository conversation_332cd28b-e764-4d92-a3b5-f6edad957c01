import { Building2, Factory, FlaskConical, Palette } from "lucide-react"

const industries = [
  {
    name: "化学工業",
    icon: FlaskConical,
    description: "原料管理・品質管理システム",
    features: ["在庫管理", "品質トレーサビリティ", "安全データ管理"]
  },
  {
    name: "製薬業界",
    icon: Building2,
    description: "規制対応・品質保証システム",
    features: ["GMP対応", "バリデーション", "文書管理"]
  },
  {
    name: "化成品製造",
    icon: Factory,
    description: "生産管理・物流システム",
    features: ["生産計画", "物流最適化", "コスト管理"]
  },
  {
    name: "化粧品業界",
    icon: Palette,
    description: "ECサイト・マーケティング",
    features: ["オンライン販売", "顧客管理", "ブランディング"]
  },
]

export function ClientShowcase() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">対応業界</h2>
          <p className="text-lg text-gray-600">化学業界の様々な分野でソリューションを提供しています</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {industries.map((industry, index) => {
            const Icon = industry.icon
            return (
              <div key={index} className="text-center bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <Icon className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-bold mb-2">{industry.name}</h3>
                <p className="text-sm text-gray-600 mb-4">{industry.description}</p>
                <div className="space-y-1">
                  {industry.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center justify-center text-xs text-gray-500">
                      <span className="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
                      {feature}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
