import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'お問い合わせ | MetaLink株式会社',
  description: 'MetaLinkへのお問い合わせはこちらから。システム開発、デジタルソリューションに関するご相談を24時間受け付けております。',
  keywords: 'お問い合わせ, MetaLink, 問い合わせフォーム, 相談, システム開発, 大阪',
  openGraph: {
    title: 'お問い合わせ | MetaLink株式会社',
    description: 'MetaLinkへのお問い合わせはこちらから。システム開発、デジタルソリューションに関するご相談を24時間受け付けております。',
    url: 'https://metalinktech.co.jp/contact',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'お問い合わせ | MetaLink株式会社',
    description: 'MetaLinkへのお問い合わせはこちらから。',
  },
}

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      {children}
    </>
  )
}
