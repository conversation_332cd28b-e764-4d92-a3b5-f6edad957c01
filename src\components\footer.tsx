import Link from "next/link"
import Image from "next/image"
import { services } from "@/config/services"

export function Footer() {
  return (
    <footer className="bg-gray-900 text-gray-300">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-white text-lg font-bold mb-4">MetaLink株式会社</h3>
            <ul className="text-sm space-y-2">          
              <li>メール: <a href="mailto:<EMAIL>" className="hover:text-white"><EMAIL></a></li>
              <li>公式サイト: <a href="https://metalinktech.co.jp" title="MetaLink - Web開発・ERPシステム・B2Bプラットフォーム開発の専門企業" className="hover:text-white">https://metalinktech.co.jp</a></li>
              <li>所在地: 〒535-0011 大阪市旭区今市2丁目20番3-303号</li>
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-bold mb-4">サービス</h3>
            <ul className="space-y-2 text-sm">
              {services.map((service, index) => (
                <li key={index}>
                  <Link 
                    href={service.link} 
                    className="hover:text-white transition-colors"
                  >
                    {service.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-bold mb-4">企業情報</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/about" className="hover:text-white">
                  会社概要
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-white">
                  お問い合わせ
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-white">
                  利用規約
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="hover:text-white">
                  プライバシーポリシー
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-white text-lg font-bold mb-4">お問い合わせ</h3>
            <div className="flex gap-4">
              <div className="text-center">
                <Image
                  src="/images/metalink-qr.png"
                  alt="MetaLinkの公式サイトのQRコード"
                  width={100}
                  height={100}
                  className="mb-2 rounded-lg shadow-md hover:shadow-lg transition-shadow"
                />
                <p className="text-xs text-gray-400">公式サイト</p>
              </div>
              <div className="text-center">
                <Image
                  src="/images/metalink-line.png"
                  alt="MetaLinkの公式LINEのQRコード"
                  width={100}
                  height={100}
                  className="mb-2 rounded-lg shadow-md hover:shadow-lg transition-shadow"
                />
                <p className="text-xs text-gray-400">LINE公式アカウント</p>
              </div>
              <div className="text-center">
                <a href="https://x.com/metalinkcorp" target="_blank" rel="noopener noreferrer" aria-label="MetaLinkの公式Twitter" title="MetaLinkの公式Twitter">
                <Image
                  src="/images/x.svg"
                  alt="MetaLinkの公式Twitter"
                  width={30}
                  height={30}
                  className="mb-2 shadow-md hover:shadow-lg transition-shadow"
                />
                </a>                
              </div>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-sm">
          <p>&copy; {new Date().getFullYear()} MetaLink株式会社 All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  )
}
