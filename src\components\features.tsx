import { CheckCircle2, Code2, Gauge, Shield } from "lucide-react"

const features = [
  {
    icon: Code2,
    title: "カスタマイズ性",
    description: "お客様のニーズに合わせて、柔軟にシステムをカスタマイズできます。",
  },
  {
    icon: Shield,
    title: "セキュリティ",
    description: "最新のセキュリティ技術を採用し、安全なシステム運用を実現します。",
  },
  {
    icon: Gauge,
    title: "高性能",
    description: "最適化された設計で、高速かつ安定したパフォーマンスを提供します。",
  },
  {
    icon: CheckCircle2,
    title: "信頼性",
    description: "24時間365日の安定稼働と充実したサポート体制を整えています。",
  },
]

export function Features() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">特徴</h2>
          <p className="text-slate-600">高品質なシステム開発を支える4つの柱</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="p-6 bg-white rounded-lg border border-slate-100"
            >
              <feature.icon className="w-12 h-12 text-blue-500 mb-4" />
              <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
              <p className="text-slate-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
