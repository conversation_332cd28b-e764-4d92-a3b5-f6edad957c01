import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'プライバシーポリシー | MetaLink株式会社',
  description: 'MetaLink株式会社のプライバシーポリシーです。当社における個人情報の取り扱い、収集する情報、利用目的、第三者提供などについて説明しています。',
  keywords: 'プライバシーポリシー, 個人情報保護方針, 個人情報の取り扱い, MetaLink',
  openGraph: {
    title: 'プライバシーポリシー | MetaLink株式会社',
    description: 'MetaLink株式会社のプライバシーポリシーです。当社における個人情報の取り扱いについて説明しています。',
    url: 'https://metalinktech.co.jp/privacy-policy',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'プライバシーポリシー | MetaLink株式会社',
    description: 'MetaLink株式会社のプライバシーポリシーです。当社における個人情報の取り扱いについて説明しています。',
  },
}

export default function PrivacyPolicyPage() {
  const policies = [
    {
      title: "個人情報の取り扱いについて",
      content: `当社は、お客様の個人情報を適切に取り扱うことが重要な責務であると認識し、以下の方針に基づき個人情報の保護に努めます。
      \n・個人情報の取得は、適法かつ公正な手段によって行います。
      \n・個人情報の利用目的を明確にし、その目的の範囲内でのみ使用します。
      \n・個人情報への不正アクセス、紛失、破壊、改ざん、漏洩などを防止するため、適切な安全管理措置を講じます。`,
    },
    {
      title: "収集する情報",
      content: `当社が収集する個人情報には以下が含まれます：
      \n・お名前
      \n・メールアドレス
      \n・会社名
      \n・電話番号
      \n・その他お問い合わせの際にご提供いただく情報`,
    },
    {
      title: "利用目的",
      content: `収集した個人情報は、以下の目的で利用させていただきます：
      \n・お問い合わせへの対応
      \n・サービスの提供
      \n・製品・サービスに関する情報のご提供
      \n・アンケートやキャンペーンの実施
      \n・サービスの品質向上`,
    },
    {
      title: "第三者への提供",
      content: `当社は、以下の場合を除き、お客様の個人情報を第三者に提供することはありません：
      \n・お客様の同意がある場合
      \n・法令に基づく場合
      \n・人の生命、身体または財産の保護のために必要がある場合
      \n・公衆衛生の向上または児童の健全な育成の推進のために特に必要がある場合`,
    },
    {
      title: "開示・訂正・利用停止",
      content: `お客様ご自身の個人情報の開示・訂正・利用停止等をご希望の場合は、お問い合わせフォームよりご連絡ください。`,
    },
  ]

  return (
    <main className="container mx-auto px-4 py-16" role="main" aria-labelledby="privacy-title">
      <h1 id="privacy-title" className="text-4xl font-bold text-center mb-12">プライバシーポリシー</h1>
      
      <div className="max-w-3xl mx-auto">
        {policies.map((policy, index) => (
          <section key={index} className="mb-8" aria-labelledby={`policy-${index}`}>
            <h2 id={`policy-${index}`} className="text-2xl font-semibold mb-4">{policy.title}</h2>
            <div className="prose max-w-none">
              {policy.content.split('\n').map((paragraph, pIndex) => (
                <p key={pIndex} className="mb-2">
                  {paragraph}
                </p>
              ))}
            </div>
          </section>
        ))}
        
        <section className="mt-12" aria-labelledby="contact-info">
          <h2 id="contact-info" className="text-2xl font-semibold mb-4">お問い合わせ</h2>
          <p>
            当社の個人情報の取り扱いに関するお問い合わせは、下記までご連絡ください：
          </p>
          <address className="mt-4 not-italic">
            <p>MetaLink株式会社</p>
            <p>Email: <EMAIL></p>
          </address>
        </section>
      </div>
    </main>
  )
}
