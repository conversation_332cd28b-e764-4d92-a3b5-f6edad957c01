server {
    listen 80;
    server_name metalinksoft.com www.metalinksoft.com;

    location / {
        return 301 https://metalinktech.co.jp$request_uri;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
    }
}

server {
    listen 443 ssl;
    http2 on;
    server_name metalinksoft.com www.metalinksoft.com;

   return 301 https://metalinktech.co.jp$request_uri;
 

    # SSL 配置
    ssl_certificate /etc/nginx/certs/metalinksoft.com/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/metalinksoft.com/metalinksoft.com.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # 安全头部
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;


    # 日志
    access_log /var/log/nginx/metalinksoft.access.log combined buffer=512k flush=1m;
    error_log /var/log/nginx/metalinksoft.error.log warn;
}

