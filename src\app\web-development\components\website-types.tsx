import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

const websiteTypes = [
  {
    title: "企業サイト",
    description: "会社概要、製品情報、技術紹介など、企業の顔となる総合的なウェブサイトを構築します。",
  },
  {
    title: "製品カタログサイト",
    description: "化学製品の詳細情報を効果的に掲載し、製品検索機能も備えたカタログサイトを開発します。",
  },
  {
    title: "ECサイト",
    description: "化学製品のオンライン販売を可能にする、安全で使いやすいECサイトを構築します。",
  },
  {
    title: "技術情報ポータル",
    description: "最新の研究成果や技術情報を共有するための専門ポータルサイトを開発します。",
  },
]

export function WebsiteTypes() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">多様なウェブサイト開発</h2>
            <div className="space-y-6">
              {websiteTypes.map((type, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold mb-2">{type.title}</h3>
                    <p className="text-gray-600">{type.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          <div className="relative aspect-square">
            <Image
              src="/images/web-development.svg"
              alt="多様なウェブサイトタイプ"
              fill
              className="object-cover rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
