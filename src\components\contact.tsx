import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

export function Contact() {
  return (
    <section id="contact" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">お問い合わせ</h2>
        <div className="max-w-xl mx-auto">
          <form className="space-y-6">
            <div>
              <label htmlFor="company" className="block text-sm font-medium mb-2">
                会社名
              </label>
              <Input id="company" required />
            </div>

            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-2">
                お名前
              </label>
              <Input id="name" required />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                メールアドレス
              </label>
              <Input type="email" id="email" required />
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium mb-2">
                お問い合わせ内容
              </label>
              <Textarea id="message" rows={5} required />
            </div>

            <Button type="submit" className="w-full">
              送信する
            </Button>
          </form>
        </div>
      </div>
    </section>
  )
}

