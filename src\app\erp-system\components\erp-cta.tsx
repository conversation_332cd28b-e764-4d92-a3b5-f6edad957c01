import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import Link from "next/link"

export function ERPCTA() {
  return (
    <section className="py-20 bg-blue-600">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">化工ERPシステムの導入をご検討ください</h2>
          <p className="text-lg mb-8  text-gray-300">
            専門コンサルタントが貴社のニーズに合わせて
            <br className="hidden md:inline" />
            最適なERPソリューションをご提案いたします
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="gap-2">
                無料相談を予約
                <ArrowRight className="w-4 h-4" />
              </Button>
            </Link>
            {/* <Button size="lg" variant="outline" className="text-blue-500 border-blue-500 hover:bg-blue-400 hover:text-white">
              資料ダウンロード
            </Button> */}
          </div>
        </div>
      </div>
    </section>
  )
}

