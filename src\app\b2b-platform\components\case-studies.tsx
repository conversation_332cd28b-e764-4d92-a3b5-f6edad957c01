import { Card, CardContent } from "@/components/ui/card"

const cases = [
  {
    company: "化学工業メーカー",
    position: "購買部長",
    content:
      "取引先の開拓から商談まで、すべてをオンラインで完結できるようになり、業務効率が大幅に向上しました。専門的な製品検索機能により、必要な原料を迅速に調達できています。",
    results: ["業務効率40%向上", "調達時間50%短縮", "取引先開拓効率化"]
  },
  {
    company: "製薬メーカー",
    position: "営業部長",
    content:
      "新規取引先との商談機会が増え、売上向上に大きく貢献しています。取引データの分析機能により、より戦略的で効果的な営業活動が可能になりました。",
    results: ["新規取引先30%増", "売上向上", "営業効率大幅改善"]
  },
  {
    company: "化成品メーカー",
    position: "代表取締役",
    content:
      "導入後、取引関連の業務時間が大幅に削減され、社員の働き方改革にもつながっています。セキュアな環境で安心して取引できることも大きな魅力です。",
    results: ["業務時間60%削減", "働き方改革実現", "セキュリティ向上"]
  },
]

export function CaseStudies() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">導入効果事例</h2>
          <p className="text-lg text-gray-600">B2Bプラットフォーム導入により実現可能な効果をご紹介します</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {cases.map((case_, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                    <p className="font-bold text-gray-900">{case_.company}</p>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">{case_.position}</p>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">{case_.content}</p>

                {/* 導入効果 */}
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-gray-900 mb-3">導入効果：</h4>
                  <div className="space-y-2">
                    {case_.results.map((result, resultIndex) => (
                      <div key={resultIndex} className="flex items-center text-sm">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                        <span className="text-gray-600">{result}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
