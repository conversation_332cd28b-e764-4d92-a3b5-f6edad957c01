import { Shield, Award, Users, Clock, Star } from "lucide-react"

const trustMetrics = [
  {
    icon: Clock,
    value: "10年+",
    label: "業界経験",
    description: "化工業界専門の深い知識"
  },
  {
    icon: Shield,
    value: "24/7",
    label: "サポート体制",
    description: "安心の継続サポート"
  },
  {
    icon: Award,
    value: "最新技術",
    label: "AI・IoT活用",
    description: "先進的なソリューション"
  },
  {
    icon: Users,
    value: "専門特化",
    label: "化工業界",
    description: "業界特有の要件に対応"
  }
]

// 認証情報は実際に取得後に追加予定
// const certifications = [
//   {
//     name: "ISO 27001",
//     description: "情報セキュリティマネジメント",
//     icon: Shield
//   },
//   {
//     name: "プライバシーマーク",
//     description: "個人情報保護体制",
//     icon: CheckCircle
//   }
// ]

const competitiveAdvantages = [
  {
    title: "化工業界専門",
    description: "10年以上の業界経験と深い専門知識",
    icon: Award
  },
  {
    title: "豊富な経験",
    description: "多様なプロジェクトでの実践的な知識",
    icon: Users
  },
  {
    title: "最新技術",
    description: "AI・IoT技術を活用した先進的ソリューション",
    icon: Star
  },
  {
    title: "万全サポート",
    description: "24時間365日の技術サポート体制",
    icon: Clock
  }
]

export function TrustIndicators() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* なぜMetaLinkが選ばれるのか */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">なぜMetaLinkが選ばれるのか？</h2>
          <p className="text-slate-600 max-w-2xl mx-auto">
            化工業界に特化した専門性と豊富な実績で、お客様のデジタル変革を確実に成功に導きます
          </p>
        </div>

        {/* 信頼指標 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {trustMetrics.map((metric, index) => {
            const Icon = metric.icon
            return (
              <div key={index} className="text-center p-6 bg-slate-50 rounded-lg">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                  <Icon className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{metric.value}</div>
                <div className="text-lg font-semibold text-gray-700 mb-1">{metric.label}</div>
                <div className="text-sm text-gray-600">{metric.description}</div>
              </div>
            )
          })}
        </div>

        {/* 競争優位性 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {competitiveAdvantages.map((advantage, index) => {
            const Icon = advantage.icon
            return (
              <div key={index} className="text-center p-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
                  <Icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold mb-2">{advantage.title}</h3>
                <p className="text-gray-600">{advantage.description}</p>
              </div>
            )
          })}
        </div>

        {/* 認証・資格 */}
        {/* <div className="bg-slate-50 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-center mb-8">認証・資格</h3>
          <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto">
            {certifications.map((cert, index) => {
              const Icon = cert.icon
              return (
                <div key={index} className="flex items-center gap-4 p-4 bg-white rounded-lg shadow-sm">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <Icon className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                  <div>
                    <div className="font-bold text-gray-900">{cert.name}</div>
                    <div className="text-sm text-gray-600">{cert.description}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </div> */}
      </div>
    </section>
  )
}
