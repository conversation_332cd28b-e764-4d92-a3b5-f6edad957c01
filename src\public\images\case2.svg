<svg width="800" height="400" viewBox="0 0 800 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="supplierGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="buyerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="platformGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 数据流动效果 -->
    <filter id="dataFlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="800" height="400" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="800" height="400" fill="url(#grid)"/>

  <!-- 商社业务背景装饰 -->
  <g opacity="0.1" fill="#3b82f6">
    <text x="50" y="100" font-size="18" transform="rotate(-15)">🏢</text>
    <text x="720" y="120" font-size="16" transform="rotate(20)">🤝</text>
    <text x="80" y="350" font-size="14" transform="rotate(-10)">📈</text>
    <text x="680" y="320" font-size="12" transform="rotate(25)">💼</text>
  </g>

  <!-- 供应商端 -->
  <g transform="translate(150,200)">
    <g filter="url(#shadow)">
      <rect x="-75" y="-60" width="150" height="120" fill="url(#supplierGradient)" rx="15"/>
      <rect x="-72" y="-57" width="144" height="114" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>

    <!-- 供应商图标 -->
    <circle cx="0" cy="-30" r="15" fill="rgba(255,255,255,0.9)"/>
    <text x="0" y="-25" text-anchor="middle" fill="#059669" font-size="16">🏭</text>

    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="14" font-weight="bold">サプライヤー</text>
    <text x="0" y="12" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Supplier</text>

    <!-- 供应商数据 -->
    <rect x="-60" y="25" width="120" height="25" fill="rgba(255,255,255,0.9)" rx="4"/>
    <text x="0" y="35" text-anchor="middle" fill="#059669" font-size="9" font-weight="bold">製品カタログ</text>
    <text x="0" y="45" text-anchor="middle" fill="#1e293b" font-size="8">1,250 items</text>
  </g>

  <!-- 中央プラットフォーム -->
  <g transform="translate(400,200)">
    <g filter="url(#shadow)">
      <rect x="-60" y="-40" width="120" height="80" fill="url(#platformGradient)" rx="12"/>
      <rect x="-57" y="-37" width="114" height="74" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="9"/>
    </g>

    <text x="0" y="-15" text-anchor="middle" fill="white" font-size="12" font-weight="bold">B2B</text>
    <text x="0" y="0" text-anchor="middle" fill="white" font-size="12" font-weight="bold">プラットフォーム</text>
    <text x="0" y="18" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">MetaLink</text>

    <!-- 处理中指示器 -->
    <circle cx="0" cy="30" r="6" fill="#22c55e">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 购买方端 -->
  <g transform="translate(650,200)">
    <g filter="url(#shadow)">
      <rect x="-75" y="-60" width="150" height="120" fill="url(#buyerGradient)" rx="15"/>
      <rect x="-72" y="-57" width="144" height="114" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>

    <!-- 购买方图标 -->
    <circle cx="0" cy="-30" r="15" fill="rgba(255,255,255,0.9)"/>
    <text x="0" y="-25" text-anchor="middle" fill="#d97706" font-size="16">🏢</text>

    <text x="0" y="-5" text-anchor="middle" fill="white" font-size="14" font-weight="bold">バイヤー</text>
    <text x="0" y="12" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Buyer</text>

    <!-- 购买方数据 -->
    <rect x="-60" y="25" width="120" height="25" fill="rgba(255,255,255,0.9)" rx="4"/>
    <text x="0" y="35" text-anchor="middle" fill="#d97706" font-size="9" font-weight="bold">発注管理</text>
    <text x="0" y="45" text-anchor="middle" fill="#1e293b" font-size="8">350 orders</text>
  </g>

  <!-- 数据流动连接线 -->
  <g stroke="#3b82f6" stroke-width="4" opacity="0.8">
    <!-- 供应商到平台 -->
    <path d="M225 200Q312 180 340 200" fill="none" stroke-dasharray="8,4" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" repeatCount="indefinite"/>
    </path>

    <!-- 平台到购买方 -->
    <path d="M460 200Q548 180 575 200" fill="none" stroke-dasharray="8,4" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" begin="1s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 数据流动指示器 -->
  <g fill="#3b82f6" opacity="0.7">
    <circle cx="280" cy="190" r="4">
      <animate attributeName="r" values="4;8;4" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="520" cy="190" r="4">
      <animate attributeName="r" values="4;8;4" dur="2s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" begin="1s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 改善效果展示 -->
  <g transform="translate(400,100)">
    <rect width="200" height="40" fill="white" rx="8" filter="url(#shadow)"/>
    <text x="100" y="18" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">取引効率化</text>
    <text x="100" y="32" text-anchor="middle" fill="#10b981" font-size="14" font-weight="bold">スムーズな連携</text>
  </g>

  <!-- 成果指标 -->
  <g transform="translate(100,100)">
    <rect width="120" height="60" fill="white" rx="8" filter="url(#shadow)"/>
    <text x="60" y="20" text-anchor="middle" fill="#1e293b" font-size="11" font-weight="bold">導入効果</text>
    <text x="60" y="35" text-anchor="middle" fill="#3b82f6" font-size="12" font-weight="bold">処理時間</text>
    <text x="60" y="50" text-anchor="middle" fill="#10b981" font-size="14" font-weight="bold">70%短縮</text>
  </g>

  <g transform="translate(580,100)">
    <rect width="120" height="60" fill="white" rx="8" filter="url(#shadow)"/>
    <text x="60" y="20" text-anchor="middle" fill="#1e293b" font-size="11" font-weight="bold">取引先満足度</text>
    <text x="60" y="35" text-anchor="middle" fill="#f59e0b" font-size="12" font-weight="bold">評価向上</text>
    <text x="60" y="50" text-anchor="middle" fill="#10b981" font-size="14" font-weight="bold">95%</text>
  </g>

  <!-- 通信状态指示 -->
  <g transform="translate(400,320)">
    <rect width="160" height="30" fill="white" rx="6" filter="url(#shadow)"/>
    <circle cx="20" cy="15" r="6" fill="#22c55e">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
    </circle>
    <text x="35" y="19" fill="#22c55e" font-size="10" font-weight="bold">リアルタイム連携中</text>
  </g>
</svg>
