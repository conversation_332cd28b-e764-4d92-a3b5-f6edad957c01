import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

const features = [
  {
    title: "業界特化のカスタマイズ",
    description: "化学製品の特性や製造プロセスに合わせた柔軟なカスタマイズが可能です。",
  },
  {
    title: "リアルタイムデータ分析",
    description: "生産状況や在庫レベルをリアルタイムで把握し、迅速な意思決定を支援します。",
  },
  {
    title: "規制遵守サポート",
    description: "化学物質規制や品質基準への準拠を自動化し、コンプライアンスリスクを低減します。",
  },
  {
    title: "クラウドベースの柔軟性",
    description: "クラウド環境での運用により、場所を問わずアクセス可能で、迅速な展開が可能です。",
  },
]

export function ERPFeatures() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="relative aspect-square">
            <Image
              src="/images/erp-features.svg"
              alt="ERPシステムの特徴"
              fill
              className="object-cover rounded-lg"
            />
          </div>
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">化工ERPの特長</h2>
            <div className="space-y-6">
              {features.map((feature, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
