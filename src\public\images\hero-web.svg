<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="browserGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="contentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="codeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="6" stdDeviation="4" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 代码高亮效果 -->
    <filter id="codeGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="600" height="600" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse">
      <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="600" height="600" fill="url(#grid)"/>

  <!-- 代码符号装饰 -->
  <g opacity="0.1" fill="#3b82f6" font-family="monospace">
    <text x="50" y="150" font-size="20" transform="rotate(-15)">&lt;/&gt;</text>
    <text x="520" y="180" font-size="16" transform="rotate(20)">{}</text>
    <text x="80" y="450" font-size="18" transform="rotate(-10)">HTML</text>
    <text x="480" y="420" font-size="14" transform="rotate(25)">CSS</text>
    <text x="30" y="300" font-size="12" transform="rotate(-20)">JavaScript</text>
  </g>

  <!-- 网站布局示意图 -->
  <g transform="translate(100,120)">
    <!-- 浏览器窗口外框 -->
    <g filter="url(#shadow)">
      <rect width="400" height="320" fill="url(#browserGradient)" rx="15"/>
      <rect x="3" y="3" width="394" height="314" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>

    <!-- 浏览器标题栏 -->
    <rect x="0" y="0" width="400" height="35" fill="url(#headerGradient)" rx="15 15 0 0"/>

    <!-- 浏览器按钮 -->
    <circle cx="20" cy="17.5" r="6" fill="#ef4444" filter="url(#shadow)"/>
    <circle cx="45" cy="17.5" r="6" fill="#eab308" filter="url(#shadow)"/>
    <circle cx="70" cy="17.5" r="6" fill="#22c55e" filter="url(#shadow)"/>

    <!-- 地址栏 -->
    <rect x="100" y="10" width="280" height="15" fill="rgba(255,255,255,0.9)" rx="7"/>
    <text x="110" y="21" fill="#64748b" font-size="10">https://metalinktech.co.jp</text>

    <!-- 网站内容区域 -->
    <rect x="15" y="50" width="370" height="255" fill="url(#contentGradient)" rx="8"/>

    <!-- 导航栏 -->
    <rect x="30" y="70" width="340" height="35" fill="#3b82f6" rx="6"/>
    <text x="50" y="90" fill="white" font-size="12" font-weight="bold">MetaLink</text>
    <text x="150" y="90" fill="rgba(255,255,255,0.9)" font-size="10">ホーム</text>
    <text x="200" y="90" fill="rgba(255,255,255,0.9)" font-size="10">サービス</text>
    <text x="260" y="90" fill="rgba(255,255,255,0.9)" font-size="10">会社概要</text>
    <text x="320" y="90" fill="rgba(255,255,255,0.9)" font-size="10">お問い合わせ</text>

    <!-- ヒーローセクション -->
    <rect x="30" y="120" width="340" height="80" fill="#f1f5f9" rx="6"/>
    <rect x="45" y="135" width="200" height="12" fill="#3b82f6" rx="2"/>
    <rect x="45" y="155" width="280" height="8" fill="#e2e8f0" rx="2"/>
    <rect x="45" y="170" width="250" height="8" fill="#e2e8f0" rx="2"/>

    <!-- コンテンツカード -->
    <g>
      <!-- カード1 -->
      <rect x="30" y="220" width="100" height="70" fill="white" rx="6" filter="url(#shadow)"/>
      <rect x="40" y="230" width="80" height="8" fill="#10b981" rx="2"/>
      <rect x="40" y="245" width="70" height="6" fill="#e2e8f0" rx="1"/>
      <rect x="40" y="255" width="60" height="6" fill="#e2e8f0" rx="1"/>

      <!-- カード2 -->
      <rect x="150" y="220" width="100" height="70" fill="white" rx="6" filter="url(#shadow)"/>
      <rect x="160" y="230" width="80" height="8" fill="#f59e0b" rx="2"/>
      <rect x="160" y="245" width="70" height="6" fill="#e2e8f0" rx="1"/>
      <rect x="160" y="255" width="60" height="6" fill="#e2e8f0" rx="1"/>

      <!-- カード3 -->
      <rect x="270" y="220" width="100" height="70" fill="white" rx="6" filter="url(#shadow)"/>
      <rect x="280" y="230" width="80" height="8" fill="#8b5cf6" rx="2"/>
      <rect x="280" y="245" width="70" height="6" fill="#e2e8f0" rx="1"/>
      <rect x="280" y="255" width="60" height="6" fill="#e2e8f0" rx="1"/>
    </g>
  </g>

  <!-- 技術スタック表示 -->
  <g transform="translate(50,460)">
    <g filter="url(#shadow)">
      <rect width="500" height="80" fill="url(#codeGradient)" rx="10"/>
      <rect x="3" y="3" width="494" height="74" fill="none" stroke="rgba(34,197,94,0.3)" stroke-width="1" rx="7"/>
    </g>

    <!-- コード行 -->
    <text x="20" y="25" fill="#22c55e" font-family="monospace" font-size="12" filter="url(#codeGlow)">&lt;div class="metalink-website"&gt;</text>
    <text x="40" y="45" fill="#60a5fa" font-family="monospace" font-size="12" filter="url(#codeGlow)">&lt;header&gt;MetaLink株式会社&lt;/header&gt;</text>
    <text x="40" y="65" fill="#fbbf24" font-family="monospace" font-size="12" filter="url(#codeGlow)">&lt;main&gt;革新的なWebソリューション&lt;/main&gt;</text>

    <!-- 技術ラベル -->
    <g transform="translate(420,10)">
      <rect width="60" height="20" fill="#22c55e" rx="10" opacity="0.8"/>
      <text x="30" y="14" text-anchor="middle" fill="white" font-size="10" font-weight="bold">HTML5</text>
    </g>
    <g transform="translate(420,35)">
      <rect width="60" height="20" fill="#3b82f6" rx="10" opacity="0.8"/>
      <text x="30" y="14" text-anchor="middle" fill="white" font-size="10" font-weight="bold">CSS3</text>
    </g>
    <g transform="translate(420,60)">
      <rect width="60" height="20" fill="#f59e0b" rx="10" opacity="0.8"/>
      <text x="30" y="14" text-anchor="middle" fill="white" font-size="10" font-weight="bold">JS</text>
    </g>
  </g>

  <!-- レスポンシブ表示アイコン -->
  <g transform="translate(520,150)" opacity="0.7">
    <!-- デスクトップ -->
    <rect x="0" y="0" width="40" height="25" fill="#64748b" rx="2"/>
    <rect x="2" y="2" width="36" height="21" fill="white" rx="1"/>
    <rect x="15" y="28" width="10" height="3" fill="#64748b" rx="1"/>

    <!-- タブレット -->
    <rect x="0" y="40" width="25" height="35" fill="#64748b" rx="3"/>
    <rect x="2" y="42" width="21" height="31" fill="white" rx="1"/>

    <!-- スマートフォン -->
    <rect x="0" y="85" width="15" height="25" fill="#64748b" rx="3"/>
    <rect x="2" y="87" width="11" height="21" fill="white" rx="1"/>
  </g>

  <!-- 動的要素 -->
  <g transform="translate(450,200)">
    <circle cx="0" cy="0" r="8" fill="#10b981" opacity="0.8">
      <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="4" text-anchor="middle" fill="white" font-size="8" font-weight="bold">NEW</text>
  </g>

  <!-- 標題 -->
  <text x="300" y="60" text-anchor="middle" fill="#1e293b" font-size="28" font-weight="bold" filter="url(#glow)">ウェブ開発</text>
  <text x="300" y="85" text-anchor="middle" fill="#64748b" font-size="14">モダンで高性能なWebサイトを構築</text>
</svg>
