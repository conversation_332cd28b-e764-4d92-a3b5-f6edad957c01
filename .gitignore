# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
.yarn/install-state.gz
.next/

# testing
coverage
.nyc_output

# next.js
.next/
out/
dist/

# production
build
.output

# misc
.DS_Store
*.pem
.idea/
.vscode/
*.swp
*.swo

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local
.env.development
.env.test
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Storybook
storybook-static/

# Cache
.cache/
.eslintcache
deploy/deploy.env
src/deploy*
