import type { Metada<PERSON> } from "next"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import "./globals.css"

export const metadata: Metadata = {
  title: "MetaLink - 化工業界のデジタル革新",
  description: "化工業界に特化したデジタルソリューションを提供し、お客様のビジネスの成長を支援します。",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ja">
      <body className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-grow">
          {children}
        </div>
        <Footer />
      </body>
    </html>
  )
}
