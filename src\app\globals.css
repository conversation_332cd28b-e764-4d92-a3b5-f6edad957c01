@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* 改进的主色调 - 更深的蓝色提高对比度 */
    --primary: 214 100% 40%;
    --primary-foreground: 210 40% 98%;

    /* 化工绿色作为次要色 */
    --secondary: 158 64% 52%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* 警示红色 */
    --accent: 0 84% 60%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 214 100% 40%;

    --radius: 0.5rem;

    /* 化工业界专用色彩 */
    --chemical-blue: 214 100% 40%;
    --chemical-green: 158 64% 52%;
    --chemical-orange: 25 95% 53%;
    --text-primary: 222.2 84% 4.9%;
    --text-secondary: 215.4 16.3% 46.9%;
  }
}

@font-face {
  font-family: 'Noto Sans JP';
  src: url('/fonts/NotoSansJP-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 100 900;
  font-display: swap;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Noto Sans JP', sans-serif;
    line-height: 1.6;
  }
  html {
    scroll-behavior: smooth;
  }

  /* 改进的标题层次 */
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight;
    color: hsl(var(--text-primary));
  }

  h2 {
    @apply text-2xl md:text-3xl lg:text-4xl font-bold leading-tight;
    color: hsl(var(--text-primary));
  }

  h3 {
    @apply text-xl md:text-2xl font-semibold leading-tight;
    color: hsl(var(--text-primary));
  }

  h4 {
    @apply text-lg md:text-xl font-semibold;
    color: hsl(var(--text-primary));
  }

  /* 改进的段落样式 */
  p {
    color: hsl(var(--text-secondary));
    line-height: 1.7;
  }
}

@layer components {
  /* 改进的按钮样式 */
  .btn-primary {
    @apply inline-flex items-center justify-center gap-2 px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-200;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center gap-2 px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg transition-all duration-300 hover:bg-blue-50 focus:outline-none focus:ring-4 focus:ring-blue-200;
  }

  /* 卡片样式改进 */
  .card-enhanced {
    @apply bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  /* 化工业界专用色彩类 */
  .text-chemical-blue {
    color: hsl(var(--chemical-blue));
  }

  .text-chemical-green {
    color: hsl(var(--chemical-green));
  }

  .text-chemical-orange {
    color: hsl(var(--chemical-orange));
  }

  .bg-chemical-blue {
    background-color: hsl(var(--chemical-blue));
  }

  .bg-chemical-green {
    background-color: hsl(var(--chemical-green));
  }

  .bg-chemical-orange {
    background-color: hsl(var(--chemical-orange));
  }
}
