import { Metadata } from 'next'
import { Breadcrumb, generateBreadcrumbJsonLd } from "@/components/breadcrumb"
import { PlatformHero } from "./components/platform-hero"
import { Features } from "./components/features"
import { HowItWorks } from "./components/how-it-works"
import { Benefits } from "./components/benefits"
import { CaseStudies } from "./components/case-studies"
import { PlatformCTA } from "./components/platform-cta"

export const metadata: Metadata = {
  title: 'B2Bプラットフォーム開発 | MetaLink株式会社',
  description: 'MetaLinkのB2Bプラットフォーム開発サービスでは、企業間取引を効率化し、ビジネスの成長を加速させるプラットフォームを構築します。受発注管理、在庫連携、取引先管理など、B2Bビジネスに必要な機能を提供します。',
  keywords: 'B2Bプラットフォーム, 企業間取引, 受発注システム, 在庫連携, 取引先管理, システム開発, MetaLink',
  alternates: {
    canonical: 'https://metalinktech.co.jp/b2b-platform',
  },
  openGraph: {
    title: 'B2Bプラットフォーム開発 | MetaLink株式会社',
    description: 'MetaLinkのB2Bプラットフォーム開発サービスでは、企業間取引を効率化し、ビジネスの成長を加速させるプラットフォームを構築します。',
    url: 'https://metalinktech.co.jp/b2b-platform',
    siteName: 'MetaLink',
    images: [
      {
        url: 'https://metalinktech.co.jp/images/hero-platform.svg',
        width: 1200,
        height: 630,
        alt: 'MetaLink B2Bプラットフォーム開発サービス',
      }
    ],
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'B2Bプラットフォーム開発 | MetaLink株式会社',
    description: 'MetaLinkのB2Bプラットフォーム開発サービスでは、企業間取引を効率化し、ビジネスの成長を加速させるプラットフォームを構築します。',
    images: ['https://metalinktech.co.jp/images/hero-platform.svg'],
  },
  robots: {
    index: true,
    follow: true,
  }
}

const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'B2Bプラットフォーム開発',
  provider: {
    '@type': 'Organization',
    name: 'MetaLink',
    url: 'https://metalinktech.co.jp',
    logo: 'https://metalinktech.co.jp/images/logo.webp'
  },
  description: 'MetaLinkのB2Bプラットフォーム開発サービスでは、企業間取引を効率化し、ビジネスの成長を加速させるプラットフォームを構築します。',
  serviceType: ['B2Bプラットフォーム', '企業間取引', '受発注システム', '在庫連携', '取引先管理'],
  areaServed: {
    '@type': 'Country',
    name: 'Japan'
  },
  offers: {
    '@type': 'Offer',
    itemOffered: [
      {
        '@type': 'Service',
        name: '受発注管理システム',
        description: '企業間の受発注プロセスを効率化するシステム'
      },
      {
        '@type': 'Service',
        name: '在庫連携システム',
        description: '在庫管理と連携を自動化するシステム'
      },
      {
        '@type': 'Service',
        name: '取引先管理システム',
        description: '取引先情報を一元管理するシステム'
      }
    ]
  }
}

const breadcrumbItems = [
  { label: 'B2Bプラットフォーム開発' }
]

const breadcrumbJsonLd = generateBreadcrumbJsonLd(breadcrumbItems)

export default function B2BPlatform() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
      />
      <Breadcrumb items={breadcrumbItems} />
      <main className="min-h-screen" role="main" aria-labelledby="platform-title">
        <PlatformHero />
        <Features />
        <HowItWorks />
        <Benefits />
        <CaseStudies />
        <PlatformCTA />
      </main>
    </>
  )
}
