
import Image from "next/image"

const cases = [
  {
    title: "化学製造業向け在庫管理システム",
    description: "効率的な在庫管理ソリューション",
    details: [
      "手動管理からの脱却で作業時間を大幅短縮",
      "リアルタイム在庫把握により欠品率を削減",
      "年間コスト削減効果を実現"
    ],
    image: "/images/case1.svg",
    link: "#",
    industry: "化学製造業",
    period: "標準導入期間：3ヶ月"
  },
  {
    title: "化学品商社向けB2Bプラットフォーム",
    description: "取引効率化プラットフォーム",
    details: [
      "取引先との連絡業務を効率化",
      "受注処理時間を大幅短縮",
      "顧客満足度の向上を実現"
    ],
    image: "/images/case2.svg",
    link: "#",
    industry: "化学品商社",
    period: "標準導入期間：2ヶ月"
  },
  {
    title: "化粧品メーカー向けECサイト・SEO",
    description: "デジタルマーケティングソリューション",
    details: [
      "オーガニックトラフィックの大幅増加",
      "コンバージョン率の改善",
      "売上高の大幅向上を実現"
    ],
    image: "/images/case3.svg",
    link: "#",
    industry: "化粧品製造業",
    period: "標準導入期間：4ヶ月"
  },
]

export function CaseStudies() {
  return (
    <section id="case-studies" className="py-20 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">導入事例</h2>
          <p className="text-slate-600 mb-2">お客様の成功事例をご紹介</p>
          <p className="text-sm text-blue-600 font-semibold">実際のROI改善データを公開</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {cases.map((case_, index) => (
            <div
              key={index}
              className="group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300"
            >
              <div className="relative h-48 bg-slate-50">
                <Image
                  src={case_.image}
                  alt={case_.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority={index === 1}
                  className="object-contain p-4"
                  loading={index === 1 ? "eager" : "lazy"}
                />
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-bold">{case_.title}</h3>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {case_.industry}
                  </span>
                </div>
                <p className="text-slate-600 font-semibold mb-3">{case_.description}</p>
                <div className="space-y-2 mb-4">
                  {case_.details.map((detail, detailIndex) => (
                    <div key={detailIndex} className="flex items-start text-sm text-slate-600">
                      <span className="text-green-500 mr-2 mt-0.5">✓</span>
                      <span>{detail}</span>
                    </div>
                  ))}
                </div>
                <div className="text-xs text-slate-500 mb-4">{case_.period}</div>
                {/* <Link
                  href={case_.link}
                  className="inline-flex items-center text-blue-500 group-hover:text-blue-600 font-semibold"
                >
                  <span className="mr-2">詳細を見る</span>
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </Link> */}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
