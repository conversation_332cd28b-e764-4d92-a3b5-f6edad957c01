<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="customizeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="realtimeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="complianceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="centerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 数据流动效果 -->
    <filter id="dataFlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="600" height="600" fill="url(#bgGradient)"/>
  
  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.2"/>
    </pattern>
  </defs>
  <rect width="600" height="600" fill="url(#grid)"/>
  
  <!-- 化工符号装饰 -->
  <g opacity="0.1" fill="#3b82f6">
    <text x="50" y="150" font-size="16" transform="rotate(-15)">⚗️</text>
    <text x="520" y="180" font-size="14" transform="rotate(20)">🧪</text>
    <text x="80" y="450" font-size="15" transform="rotate(-10)">⚡</text>
    <text x="480" y="420" font-size="13" transform="rotate(25)">☁️</text>
  </g>
  
  <!-- 中央ERP核心 -->
  <g transform="translate(300,300)" filter="url(#shadow)">
    <circle cx="0" cy="0" r="60" fill="url(#centerGradient)"/>
    <circle cx="0" cy="0" r="55" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <text x="0" y="-10" text-anchor="middle" fill="white" font-size="14" font-weight="bold">化工ERP</text>
    <text x="0" y="8" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">統合システム</text>
    <text x="0" y="25" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="8">🏭 Chemical</text>
  </g>
  
  <!-- 特長1: 業界特化のカスタマイズ (左上) -->
  <g transform="translate(150,150)">
    <g filter="url(#shadow)">
      <rect x="-60" y="-40" width="120" height="80" fill="url(#customizeGradient)" rx="15"/>
      <rect x="-57" y="-37" width="114" height="74" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="0" y="-15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">業界特化</text>
    <text x="0" y="0" text-anchor="middle" fill="white" font-size="11" font-weight="bold">カスタマイズ</text>
    <text x="0" y="18" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">⚙️ Customize</text>
    
    <!-- カスタマイズアイコン -->
    <g transform="translate(-25,-25)" fill="rgba(255,255,255,0.6)">
      <rect x="0" y="0" width="8" height="8" rx="1"/>
      <rect x="12" y="0" width="8" height="8" rx="1"/>
      <rect x="24" y="0" width="8" height="8" rx="1"/>
      <rect x="36" y="0" width="8" height="8" rx="1"/>
    </g>
  </g>
  
  <!-- 特長2: リアルタイムデータ分析 (右上) -->
  <g transform="translate(450,150)">
    <g filter="url(#shadow)">
      <rect x="-60" y="-40" width="120" height="80" fill="url(#realtimeGradient)" rx="15"/>
      <rect x="-57" y="-37" width="114" height="74" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="0" y="-15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">リアルタイム</text>
    <text x="0" y="0" text-anchor="middle" fill="white" font-size="11" font-weight="bold">データ分析</text>
    <text x="0" y="18" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">📊 Real-time</text>
    
    <!-- データ分析チャート -->
    <g transform="translate(-20,-20)" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none">
      <path d="M0,20 L10,15 L20,10 L30,5 L40,8"/>
      <circle cx="40" cy="8" r="2" fill="rgba(255,255,255,0.9)">
        <animate attributeName="r" values="2;4;2" dur="2s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>
  
  <!-- 特長3: 規制遵守サポート (左下) -->
  <g transform="translate(150,450)">
    <g filter="url(#shadow)">
      <rect x="-60" y="-40" width="120" height="80" fill="url(#complianceGradient)" rx="15"/>
      <rect x="-57" y="-37" width="114" height="74" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="0" y="-15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">規制遵守</text>
    <text x="0" y="0" text-anchor="middle" fill="white" font-size="11" font-weight="bold">サポート</text>
    <text x="0" y="18" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">🛡️ Compliance</text>
    
    <!-- 規制チェックマーク -->
    <g transform="translate(-15,-15)" fill="rgba(255,255,255,0.7)">
      <circle cx="15" cy="10" r="12" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
      <path d="M8,10 L13,15 L22,6" stroke="rgba(255,255,255,0.9)" stroke-width="2" fill="none"/>
    </g>
  </g>
  
  <!-- 特長4: クラウドベースの柔軟性 (右下) -->
  <g transform="translate(450,450)">
    <g filter="url(#shadow)">
      <rect x="-60" y="-40" width="120" height="80" fill="url(#cloudGradient)" rx="15"/>
      <rect x="-57" y="-37" width="114" height="74" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="0" y="-15" text-anchor="middle" fill="white" font-size="11" font-weight="bold">クラウドベース</text>
    <text x="0" y="0" text-anchor="middle" fill="white" font-size="11" font-weight="bold">柔軟性</text>
    <text x="0" y="18" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">☁️ Cloud</text>
    
    <!-- クラウドアイコン -->
    <g transform="translate(-15,-20)" fill="rgba(255,255,255,0.7)">
      <ellipse cx="15" cy="15" rx="12" ry="6"/>
      <ellipse cx="8" cy="12" rx="8" ry="4"/>
      <ellipse cx="22" cy="12" rx="8" ry="4"/>
    </g>
  </g>
  
  <!-- 接続線（動的効果） -->
  <g stroke="#64748b" stroke-width="3" opacity="0.6">
    <!-- 中央から各特長への接続 -->
    <path d="M240,240 Q195,195 150,150" fill="none" stroke-dasharray="6,3" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;9" dur="3s" repeatCount="indefinite"/>
    </path>
    <path d="M360,240 Q405,195 450,150" fill="none" stroke-dasharray="6,3" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;9" dur="3s" begin="0.75s" repeatCount="indefinite"/>
    </path>
    <path d="M240,360 Q195,405 150,450" fill="none" stroke-dasharray="6,3" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;9" dur="3s" begin="1.5s" repeatCount="indefinite"/>
    </path>
    <path d="M360,360 Q405,405 450,450" fill="none" stroke-dasharray="6,3" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;9" dur="3s" begin="2.25s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- データフロー指示器 -->
  <g fill="#3b82f6" opacity="0.7">
    <circle cx="195" cy="195" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="405" cy="195" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" begin="0.75s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" begin="0.75s" repeatCount="indefinite"/>
    </circle>
    <circle cx="195" cy="405" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" begin="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" begin="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="405" cy="405" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" begin="2.25s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" begin="2.25s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 标题 -->
  <text x="300" y="60" text-anchor="middle" fill="#1e293b" font-size="28" font-weight="bold" filter="url(#glow)">化工ERP特長</text>
  <text x="300" y="85" text-anchor="middle" fill="#64748b" font-size="14">業界特化型統合管理システム</text>
</svg>
