module.exports = {
  apps: [{
    name: process.env.DEPLOY_APP_NAME || 'metalinksoft',
    script: 'node_modules/next/dist/bin/next',
    args: 'start',
    instances: 2,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: process.env.DEPLOY_NODE_ENV || 'production',
      PORT: process.env.DEPLOY_PORT || 4000,
      HOST: process.env.DEPLOY_HOST || '0.0.0.0',
      BASE_URL: process.env.DEPLOY_BASE_URL || 'http://localhost:4000'
    },
    error_file: 'logs/error.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true,
    merge_logs: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
} 