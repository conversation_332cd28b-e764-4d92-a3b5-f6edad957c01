import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

const services = [
  {
    title: "キーワード調査・分析",
    description: "化学業界に関連する高価値なキーワードを特定し、最適な使用戦略を立案します。",
  },
  {
    title: "オンページSEO最適化",
    description: "ウェブサイトの構造、コンテンツ、メタデータを最適化し、検索エンジンの評価を向上させます。",
  },
  {
    title: "コンテンツマーケティング",
    description: "化学業界に特化した高品質なコンテンツを作成し、オーガニックトラフィックを増加させます。",
  },
  {
    title: "テクニカルSEO",
    description: "サイトの読み込み速度、モバイル対応、構造化データなど、技術的な側面を最適化します。",
  },
]

export function SEOServices() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">提供するSEOサービス</h2>
            <div className="space-y-6">
              {services.map((service, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                    <p className="text-gray-600">{service.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          <div className="relative aspect-square">
            <Image
              src="/images/seo.svg"
              alt="SEOサービスのイメージ"
              fill
              className="object-cover rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
