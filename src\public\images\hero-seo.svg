<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="searchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="resultGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="rankingGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.15"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 搜索图标路径 -->
    <path id="searchIcon" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"/>
  </defs>

  <!-- 背景 -->
  <rect width="600" height="600" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="600" height="600" fill="url(#grid)"/>

  <!-- SEO关键词云 -->
  <g opacity="0.1" fill="#3b82f6">
    <text x="50" y="150" font-size="12" transform="rotate(-15)">キーワード</text>
    <text x="520" y="180" font-size="10" transform="rotate(20)">検索順位</text>
    <text x="80" y="450" font-size="11" transform="rotate(-10)">コンテンツ</text>
    <text x="480" y="420" font-size="9" transform="rotate(25)">アクセス向上</text>
  </g>

  <!-- 搜索引擎结果页面 -->
  <g transform="translate(100,120)">
    <!-- 搜索框 -->
    <g filter="url(#shadow)">
      <rect width="400" height="50" fill="white" stroke="url(#searchGradient)" stroke-width="3" rx="25"/>
      <rect x="3" y="3" width="394" height="44" fill="none" stroke="rgba(59,130,246,0.2)" stroke-width="1" rx="22"/>
    </g>

    <!-- 搜索图标 -->
    <circle cx="370" cy="25" r="18" fill="url(#searchGradient)" filter="url(#shadow)"/>
    <g transform="translate(362,17)" fill="white">
      <circle cx="4" cy="4" r="3" fill="none" stroke="white" stroke-width="1.5"/>
      <path d="m6.5 6.5 2 2" stroke="white" stroke-width="1.5"/>
    </g>

    <!-- 搜索文字 -->
    <text x="25" y="32" fill="#64748b" font-size="14">MetaLink株式会社 SEO対策</text>

    <!-- 搜索结果 -->
    <g transform="translate(0,70)">
      <!-- 结果1 - 排名第1 -->
      <g filter="url(#shadow)">
        <rect width="400" height="90" fill="url(#resultGradient)" rx="8"/>
        <rect x="2" y="2" width="396" height="86" fill="none" stroke="rgba(16,185,129,0.3)" stroke-width="2" rx="6"/>
      </g>
      <circle cx="15" cy="15" r="8" fill="#10b981"/>
      <text x="15" y="20" text-anchor="middle" fill="white" font-size="10" font-weight="bold">1</text>
      <rect x="35" y="12" width="180" height="16" fill="#10b981" rx="2"/>
      <text x="40" y="23" fill="white" font-size="11" font-weight="bold">MetaLink株式会社 - 公式サイト</text>
      <rect x="35" y="35" width="320" height="8" fill="#e2e8f0" rx="2"/>
      <rect x="35" y="50" width="280" height="8" fill="#e2e8f0" rx="2"/>
      <text x="35" y="75" fill="#059669" font-size="10">https://metalinktech.co.jp</text>

      <!-- 结果2 - 排名第2 -->
      <g transform="translate(0,110)">
        <g filter="url(#shadow)">
          <rect width="400" height="90" fill="url(#resultGradient)" rx="8"/>
          <rect x="2" y="2" width="396" height="86" fill="none" stroke="rgba(59,130,246,0.2)" stroke-width="1" rx="6"/>
        </g>
        <circle cx="15" cy="15" r="8" fill="#3b82f6"/>
        <text x="15" y="20" text-anchor="middle" fill="white" font-size="10" font-weight="bold">2</text>
        <rect x="35" y="12" width="160" height="16" fill="#3b82f6" rx="2"/>
        <text x="40" y="23" fill="white" font-size="11" font-weight="bold">競合他社サイト</text>
        <rect x="35" y="35" width="300" height="8" fill="#e2e8f0" rx="2"/>
        <rect x="35" y="50" width="250" height="8" fill="#e2e8f0" rx="2"/>
        <text x="35" y="75" fill="#3b82f6" font-size="10">https://competitor.com</text>
      </g>

      <!-- 结果3 - 排名第3 -->
      <g transform="translate(0,220)">
        <g filter="url(#shadow)">
          <rect width="400" height="90" fill="url(#resultGradient)" rx="8"/>
          <rect x="2" y="2" width="396" height="86" fill="none" stroke="rgba(100,116,139,0.2)" stroke-width="1" rx="6"/>
        </g>
        <circle cx="15" cy="15" r="8" fill="#64748b"/>
        <text x="15" y="20" text-anchor="middle" fill="white" font-size="10" font-weight="bold">3</text>
        <rect x="35" y="12" width="140" height="16" fill="#64748b" rx="2"/>
        <text x="40" y="23" fill="white" font-size="11" font-weight="bold">その他のサイト</text>
        <rect x="35" y="35" width="280" height="8" fill="#e2e8f0" rx="2"/>
        <rect x="35" y="50" width="220" height="8" fill="#e2e8f0" rx="2"/>
        <text x="35" y="75" fill="#64748b" font-size="10">https://other-site.com</text>
      </g>
    </g>
  </g>

  <!-- SEO指标仪表板 -->
  <g transform="translate(50,480)">
    <g filter="url(#shadow)">
      <rect width="500" height="40" fill="white" rx="8"/>
    </g>
    <text x="20" y="18" fill="#1e293b" font-size="12" font-weight="bold">検索順位:</text>

    <!-- 排名进度条 -->
    <rect x="20" y="22" width="460" height="12" fill="#e2e8f0" rx="6"/>
    <rect x="20" y="22" width="368" height="12" fill="url(#rankingGradient)" rx="6"/>

    <!-- 排名标签 -->
    <text x="400" y="31" fill="white" font-size="10" font-weight="bold">1位達成!</text>

    <!-- SEO指标 -->
    <g transform="translate(420,5)">
      <circle cx="0" cy="0" r="12" fill="#10b981">
        <animate attributeName="r" values="12;15;12" dur="2s" repeatCount="indefinite"/>
      </circle>
      <text x="0" y="4" text-anchor="middle" fill="white" font-size="8" font-weight="bold">TOP</text>
    </g>
  </g>

  <!-- 上升箭头动画 -->
  <g transform="translate(520,200)" fill="#10b981" opacity="0.8">
    <path d="M0,20 L10,0 L20,20 L15,20 L15,40 L5,40 L5,20 Z">
      <animateTransform attributeName="transform" type="translate" values="0,0; 0,-10; 0,0" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 标题 -->
  <text x="300" y="60" text-anchor="middle" fill="#1e293b" font-size="28" font-weight="bold" filter="url(#glow)">SEO対策</text>
  <text x="300" y="85" text-anchor="middle" fill="#64748b" font-size="14">検索エンジン最適化で上位表示を実現</text>
</svg>
