<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="platformGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="supplierGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="buyerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="600" height="600" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="600" height="600" fill="url(#grid)"/>

  <!-- 平台示意图 -->
  <g transform="translate(50,50)">
    <!-- 连接线（放在节点下方） -->
    <g stroke="#64748b" stroke-width="3" opacity="0.6">
      <!-- 动态虚线效果 -->
      <line x1="140" y1="150" x2="200" y2="200" stroke-dasharray="5,5">
        <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="150" y1="310" x2="200" y2="280" stroke-dasharray="5,5">
        <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="300" y1="200" x2="350" y2="150" stroke-dasharray="5,5">
        <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="300" y1="280" x2="360" y2="310" stroke-dasharray="5,5">
        <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite"/>
      </line>
    </g>

    <!-- 中心平台 -->
    <g filter="url(#shadow)">
      <rect x="200" y="200" width="100" height="80" fill="url(#platformGradient)" rx="15"/>
      <rect x="205" y="205" width="90" height="70" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1" rx="12"/>
    </g>
    <text x="250" y="235" text-anchor="middle" fill="white" font-size="14" font-weight="bold">MetaLink</text>
    <text x="250" y="255" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">プラットフォーム</text>

    <!-- 供应商节点 -->
    <g>
      <!-- 供应商A -->
      <g filter="url(#shadow)">
        <circle cx="100" cy="150" r="45" fill="url(#supplierGradient)"/>
        <circle cx="100" cy="150" r="40" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
      </g>
      <text x="100" y="145" text-anchor="middle" fill="white" font-size="11" font-weight="bold">供給者A</text>
      <text x="100" y="158" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">Supplier</text>

      <!-- 供应商B -->
      <g filter="url(#shadow)">
        <circle cx="150" cy="350" r="45" fill="url(#supplierGradient)"/>
        <circle cx="150" cy="350" r="40" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
      </g>
      <text x="150" y="345" text-anchor="middle" fill="white" font-size="11" font-weight="bold">供給者B</text>
      <text x="150" y="358" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">Supplier</text>
    </g>

    <!-- 买家节点 -->
    <g>
      <!-- 买家A -->
      <g filter="url(#shadow)">
        <circle cx="390" cy="150" r="45" fill="url(#buyerGradient)"/>
        <circle cx="390" cy="150" r="40" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
      </g>
      <text x="390" y="145" text-anchor="middle" fill="white" font-size="11" font-weight="bold">購入者A</text>
      <text x="390" y="158" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">Buyer</text>

      <!-- 买家B -->
      <g filter="url(#shadow)">
        <circle cx="400" cy="350" r="45" fill="url(#buyerGradient)"/>
        <circle cx="400" cy="350" r="40" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
      </g>
      <text x="400" y="345" text-anchor="middle" fill="white" font-size="11" font-weight="bold">購入者B</text>
      <text x="400" y="358" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="8">Buyer</text>
    </g>

    <!-- 数据流动指示器 -->
    <g fill="#3b82f6" opacity="0.7">
      <circle cx="170" cy="175" r="3">
        <animate attributeName="r" values="3;6;3" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="175" cy="320" r="3">
        <animate attributeName="r" values="3;6;3" dur="2s" begin="0.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" begin="0.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="330" cy="175" r="3">
        <animate attributeName="r" values="3;6;3" dur="2s" begin="1s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" begin="1s" repeatCount="indefinite"/>
      </circle>
      <circle cx="335" cy="320" r="3">
        <animate attributeName="r" values="3;6;3" dur="2s" begin="1.5s" repeatCount="indefinite"/>
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" begin="1.5s" repeatCount="indefinite"/>
      </circle>
    </g>
  </g>

  <!-- 标题 -->
  <text x="300" y="520" text-anchor="middle" fill="#1e293b" font-size="28" font-weight="bold" filter="url(#glow)">B2Bプラットフォーム</text>
  <text x="300" y="545" text-anchor="middle" fill="#64748b" font-size="14">効率的なビジネス連携を実現</text>
</svg>
