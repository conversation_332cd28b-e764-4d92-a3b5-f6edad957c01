import { Metadata } from 'next'
import { Breadcrumb, generateBreadcrumbJsonLd } from "@/components/breadcrumb"
import { WebDevRelatedServices } from "@/components/related-services"
import { WebDevHero } from "./components/web-dev-hero"
import { WebDevFeatures } from "./components/web-dev-features"
import { WebsiteTypes } from "./components/website-types"
import { DevelopmentProcess } from "./components/development-process"
import { ClientShowcase } from "./components/client-showcase"
import { WebDevCTA } from "./components/web-dev-cta"

export const metadata: Metadata = {
  title: 'Web開発サービス | MetaLink株式会社',
  description: 'MetaLinkのWeb開発サービスでは、最新のテクノロジーを活用した高品質なWebサイト・Webアプリケーションを開発します。企業サイト、ECサイト、業務システムなど、お客様のニーズに合わせた最適なソリューションを提供します。',
  keywords: 'Web開発, Webアプリケーション開発, システム開発, ECサイト開発, 企業サイト制作, React, Next.js, MetaLink',
  alternates: {
    canonical: 'https://metalinktech.co.jp/web-development',
  },
  openGraph: {
    title: 'Web開発サービス | MetaLink株式会社',
    description: 'MetaLinkのWeb開発サービスでは、最新のテクノロジーを活用した高品質なWebサイト・Webアプリケーションを開発します。',
    url: 'https://metalinktech.co.jp/web-development',
    siteName: 'MetaLink',
    locale: 'ja_JP',
    type: 'website',
    images: [
      {
        url: 'https://metalinktech.co.jp/images/hero-web.svg',
        width: 1200,
        height: 630,
        alt: 'Web開発サービス',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Web開発サービス | MetaLink株式会社',
    description: 'MetaLinkのWeb開発サービスでは、最新のテクノロジーを活用した高品質なWebサイト・Webアプリケーションを開発します。',
    images: ['https://metalinktech.co.jp/images/hero-web.svg'],
  },
  robots: {
    index: true,
    follow: true,
  }
}

const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Web開発サービス',
  provider: {
    '@type': 'Organization',
    name: 'MetaLink',
    url: 'https://metalinktech.co.jp',
    logo: 'https://metalinktech.co.jp/images/logo.webp'
  },
  description: 'MetaLinkのWeb開発サービスでは、最新のテクノロジーを活用した高品質なWebサイト・Webアプリケーションを開発します。',
  serviceType: ['Web開発', 'Webアプリケーション開発', 'システム開発', 'ECサイト開発', '企業サイト制作'],
  areaServed: {
    '@type': 'Country',
    name: 'Japan'
  },
  offers: {
    '@type': 'Offer',
    itemOffered: [
      {
        '@type': 'Service',
        name: '企業サイト制作',
        description: '企業のブランド価値を高める高品質なWebサイトの制作'
      },
      {
        '@type': 'Service',
        name: 'ECサイト開発',
        description: '売上向上を実現する機能性の高いECサイトの開発'
      },
      {
        '@type': 'Service',
        name: 'Webアプリケーション開発',
        description: '業務効率化を実現するカスタムWebアプリケーションの開発'
      }
    ]
  }
}

export default function WebDevelopment() {
  const breadcrumbItems = [
    { label: 'サービス' },
    { label: 'Web開発サービス' }
  ]

  const breadcrumbJsonLd = generateBreadcrumbJsonLd(breadcrumbItems)

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
      />
      <div className="pt-20">
        <Breadcrumb items={breadcrumbItems} />
        <main className="min-h-screen" role="main" aria-labelledby="web-dev-title">
          <WebDevHero />
          <WebDevFeatures />
          <WebsiteTypes />
          <DevelopmentProcess />
          <ClientShowcase />
          <WebDevRelatedServices />
          <WebDevCTA />
        </main>
      </div>
    </>
  )
}
