import { Search, BarChart2, Globe, FileText, Users, Zap } from "lucide-react"

const features = [
  {
    icon: Search,
    title: "業界特化キーワード分析",
    description: "化学業界特有の専門用語やトレンドを考慮したキーワード戦略を立案します。",
  },
  {
    icon: BarChart2,
    title: "詳細な分析とレポート",
    description: "SEO施策の効果を可視化し、継続的な改善につなげます。",
  },
  {
    icon: Globe,
    title: "ローカルSEO対策",
    description: "地域に根ざした企業の検索露出を強化し、地元での認知度を高めます。",
  },
  {
    icon: FileText,
    title: "専門的なコンテンツ最適化",
    description: "化学製品や技術に関する専門的な記事の最適化を行います。",
  },
  {
    icon: Users,
    title: "競合分析",
    description: "業界内の競合他社のSEO戦略を分析し、差別化を図ります。",
  },
  {
    icon: Zap,
    title: "技術的SEO最適化",
    description: "サイトの読み込み速度や構造を最適化し、検索エンジンからの評価を高めます。",
  },
]

export function SEOFeatures() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">化工業界に特化したSEO戦略</h2>
          <p className="text-lg text-gray-600">業界特有のニーズに応える、専門的なSEOサービス</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="p-6 border rounded-lg hover:shadow-lg transition-shadow">
              <feature.icon className="w-12 h-12 text-blue-600 mb-4" />
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

