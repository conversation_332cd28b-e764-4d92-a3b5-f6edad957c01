import Link from "next/link"
import { ArrowRight } from "lucide-react"
import { services } from "@/config/services"

interface RelatedServicesProps {
  currentService?: string
  title?: string
  description?: string
}

export function RelatedServices({ 
  currentService, 
  title = "関連サービス",
  description = "その他のサービスもご検討ください"
}: RelatedServicesProps) {
  // 現在のサービスを除外した関連サービスを取得
  const relatedServices = services.filter(service => 
    currentService ? service.link !== currentService : true
  ).slice(0, 3)

  return (
    <section className="py-16 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{title}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">{description}</p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {relatedServices.map((service, index) => {
            const Icon = service.icon
            return (
              <Link
                key={index}
                href={service.link}
                className="group bg-white rounded-lg p-6 shadow-sm hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg mr-4">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold group-hover:text-blue-600 transition-colors">
                    {service.title}
                  </h3>
                </div>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {service.description}
                </p>
                <div className="flex items-center text-blue-600 group-hover:text-blue-700">
                  <span className="mr-2 font-medium">詳細を見る</span>
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </div>
              </Link>
            )
          })}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            どのサービスが最適かお悩みですか？
          </p>
          <Link
            href="/contact"
            className="inline-flex items-center gap-2 bg-blue-600 text-white px-8 py-4 rounded-lg hover:bg-blue-700 transition-colors font-semibold shadow-lg hover:shadow-xl"
          >
            無料相談で最適なソリューションを提案
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}

// 特定のページ用のカスタマイズされた関連サービス
export function WebDevRelatedServices() {
  return (
    <RelatedServices
      currentService="/web-development"
      title="その他のデジタルソリューション"
      description="Webサイトと連携して、さらなる業務効率化を実現しませんか？"
    />
  )
}

export function ERPRelatedServices() {
  return (
    <RelatedServices
      currentService="/erp-system"
      title="ERPと連携可能なサービス"
      description="ERPシステムと組み合わせて、包括的なデジタル変革を実現"
    />
  )
}

export function B2BRelatedServices() {
  return (
    <RelatedServices
      currentService="/b2b-platform"
      title="B2Bプラットフォーム関連サービス"
      description="プラットフォームの効果を最大化する追加サービス"
    />
  )
}

export function SEORelatedServices() {
  return (
    <RelatedServices
      currentService="/seo-optimization"
      title="SEO効果を高める関連サービス"
      description="SEO対策と組み合わせて、総合的なデジタルマーケティングを実現"
    />
  )
}
