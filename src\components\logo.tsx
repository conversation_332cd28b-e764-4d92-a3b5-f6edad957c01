interface LogoProps {
    width?: number
    height?: number
    color?: string
  }
  
  export function Logo({ width = 200, height = 60, color = "#3B82F6" }: LogoProps) {
    return (
      <svg width={width} height={height} viewBox="0 0 150 60" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g transform="translate(0, 15)">
          <path
            d="M10 20 L30 0 L50 20 L70 0 L90 20"
            stroke={color}
            strokeWidth="4"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <circle cx="100" cy="20" r="8" fill={color} />
          <path
            d="M110 0 L130 0 L130 20 L110 20 Z"
            stroke={color}
            strokeWidth="4"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
      </svg>
    )
  }