#!/bin/bash

# 配置信息
APP_NAME="metalinkweb"
DEPLOY_PATH="/mywww/websites/metalinksoft"
NODE_ENV="production"
PORT=4000

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        error "$1 is not installed"
    fi
}

# 检查必要的命令
check_command node
check_command npm
check_command pm2

# 进入部署目录
cd $DEPLOY_PATH || error "Failed to enter deployment directory"

# 设置目录权限
info "Setting directory permissions..."
sudo chown -R $USER:$(id -gn $USER) .
chmod -R 755 .
chmod -R 777 .next
chmod -R 777 logs

# 安装依赖
info "Installing dependencies..."
npm install || error "Failed to install dependencies"

# 安装next命令行工具
info "Installing next command line tool..."
npm install -g next || error "Failed to install next command line tool"

# 构建项目
info "Building project..."
npm run build || error "Failed to build project"

# 再次设置.next目录权限
chmod -R 777 .next

# 使用 PM2 管理进程
info "Starting/restarting application with PM2..."

# 检查ecosystem.config.js是否存在
if [ ! -f "ecosystem.config.js" ]; then
    error "ecosystem.config.js not found"
fi

# 停止并删除现有进程
if pm2 list | grep -q "$APP_NAME"; then
    pm2 delete $APP_NAME || error "Failed to delete existing application"
fi

# 使用ecosystem.config.js启动应用
pm2 start ecosystem.config.js || error "Failed to start application"

# 保存 PM2 进程列表
pm2 save

info "Deployment completed successfully!" 