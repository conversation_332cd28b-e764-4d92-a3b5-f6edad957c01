import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

const benefits = [
  {
    title: "取引コストの削減",
    description: "従来の取引プロセスを効率化し、コストを最大50%削減できます。",
    metric: "50%削減",
  },
  {
    title: "取引先の開拓",
    description: "新規取引先との商談機会が平均3倍に増加します。",
    metric: "3倍増加",
  },
  {
    title: "業務効率の向上",
    description: "取引関連の業務時間を平均60%短縮できます。",
    metric: "60%短縮",
  },
]

export function Benefits() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">導入効果</h2>
            <div className="space-y-6">
              {benefits.map((benefit, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                        <p className="text-gray-600">{benefit.description}</p>
                      </div>
                      <span className="text-2xl font-bold text-blue-600">{benefit.metric}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          <div className="relative aspect-square">
            <Image
              src="/images/b2b-benefits.svg?v=0626"
              alt="導入効果のグラフ"
              fill
              className="object-cover rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
