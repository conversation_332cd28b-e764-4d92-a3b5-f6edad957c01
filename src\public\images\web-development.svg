<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="desktopGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="tabletGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="mobileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="4" dy="6" stdDeviation="5" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 屏幕反光效果 -->
    <filter id="screenGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="600" height="600" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse">
      <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="600" height="600" fill="url(#grid)"/>

  <!-- 技术符号装饰 -->
  <g opacity="0.1" fill="#3b82f6" font-family="monospace">
    <text x="50" y="150" font-size="18" transform="rotate(-15)">HTML5</text>
    <text x="520" y="180" font-size="16" transform="rotate(20)">CSS3</text>
    <text x="80" y="450" font-size="14" transform="rotate(-10)">JS</text>
    <text x="480" y="420" font-size="12" transform="rotate(25)">React</text>
    <text x="30" y="300" font-size="16" transform="rotate(-20)">&lt;/&gt;</text>
  </g>

  <!-- 响应式连接线 -->
  <g stroke="#64748b" stroke-width="2" opacity="0.4" stroke-dasharray="5,5">
    <path d="M300,120 Q200,200 150,300" fill="none">
      <animate attributeName="stroke-dashoffset" values="0;10" dur="3s" repeatCount="indefinite"/>
    </path>
    <path d="M300,120 Q400,200 450,300" fill="none">
      <animate attributeName="stroke-dashoffset" values="0;10" dur="3s" begin="1s" repeatCount="indefinite"/>
    </path>
    <path d="M300,120 Q350,250 380,400" fill="none">
      <animate attributeName="stroke-dashoffset" values="0;10" dur="3s" begin="2s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 桌面设备 -->
  <g transform="translate(80,140)">
    <!-- 显示器外框 -->
    <g filter="url(#shadow)">
      <rect width="440" height="280" fill="url(#desktopGradient)" rx="15"/>
      <rect x="3" y="3" width="434" height="274" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>

    <!-- 屏幕 -->
    <rect x="20" y="20" width="400" height="225" fill="url(#screenGradient)" rx="8" filter="url(#screenGlow)"/>
    <rect x="22" y="22" width="396" height="221" fill="none" stroke="rgba(59,130,246,0.2)" stroke-width="1" rx="6"/>

    <!-- 浏览器界面 -->
    <rect x="30" y="30" width="380" height="25" fill="#e2e8f0" rx="4"/>
    <circle cx="45" cy="42.5" r="4" fill="#ef4444"/>
    <circle cx="60" cy="42.5" r="4" fill="#eab308"/>
    <circle cx="75" cy="42.5" r="4" fill="#22c55e"/>
    <rect x="100" y="37" width="200" height="11" fill="white" rx="5"/>
    <text x="110" y="46" fill="#64748b" font-size="8">metalinktech.co.jp</text>

    <!-- 网站内容 -->
    <rect x="40" y="70" width="240" height="20" fill="#3b82f6" rx="3"/>
    <text x="50" y="83" fill="white" font-size="10" font-weight="bold">MetaLink株式会社</text>

    <rect x="40" y="105" width="360" height="8" fill="#e2e8f0" rx="2"/>
    <rect x="40" y="120" width="340" height="8" fill="#e2e8f0" rx="2"/>
    <rect x="40" y="135" width="320" height="8" fill="#e2e8f0" rx="2"/>

    <!-- 服务卡片 -->
    <rect x="40" y="160" width="110" height="70" fill="white" rx="6" filter="url(#shadow)"/>
    <rect x="50" y="170" width="90" height="10" fill="#10b981" rx="2"/>
    <rect x="50" y="185" width="80" height="6" fill="#e2e8f0" rx="1"/>
    <rect x="50" y="195" width="70" height="6" fill="#e2e8f0" rx="1"/>

    <rect x="170" y="160" width="110" height="70" fill="white" rx="6" filter="url(#shadow)"/>
    <rect x="180" y="170" width="90" height="10" fill="#f59e0b" rx="2"/>
    <rect x="180" y="185" width="80" height="6" fill="#e2e8f0" rx="1"/>
    <rect x="180" y="195" width="70" height="6" fill="#e2e8f0" rx="1"/>

    <rect x="300" y="160" width="110" height="70" fill="white" rx="6" filter="url(#shadow)"/>
    <rect x="310" y="170" width="90" height="10" fill="#8b5cf6" rx="2"/>
    <rect x="310" y="185" width="80" height="6" fill="#e2e8f0" rx="1"/>
    <rect x="310" y="195" width="70" height="6" fill="#e2e8f0" rx="1"/>

    <!-- 显示器底座 -->
    <rect x="200" y="250" width="40" height="15" fill="url(#desktopGradient)" rx="3"/>
    <rect x="180" y="265" width="80" height="8" fill="url(#desktopGradient)" rx="4"/>

    <!-- 设备标签 -->
    <text x="220" y="295" text-anchor="middle" fill="#1e293b" font-size="12" font-weight="bold">Desktop</text>
    <text x="220" y="310" text-anchor="middle" fill="#64748b" font-size="10">1920×1080</text>
  </g>

  <!-- 平板设备 -->
  <g transform="translate(120,430)">
    <!-- 平板外框 -->
    <g filter="url(#shadow)">
      <rect width="180" height="130" fill="url(#tabletGradient)" rx="12"/>
      <rect x="3" y="3" width="174" height="124" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="9"/>
    </g>

    <!-- 屏幕 -->
    <rect x="15" y="15" width="150" height="100" fill="url(#screenGradient)" rx="6" filter="url(#screenGlow)"/>

    <!-- 浏览器界面 -->
    <rect x="20" y="20" width="140" height="15" fill="#e2e8f0" rx="2"/>
    <circle cx="28" cy="27.5" r="2" fill="#ef4444"/>
    <circle cx="36" cy="27.5" r="2" fill="#eab308"/>
    <circle cx="44" cy="27.5" r="2" fill="#22c55e"/>

    <!-- 网站内容（平板适配） -->
    <rect x="25" y="45" width="100" height="12" fill="#3b82f6" rx="2"/>
    <text x="30" y="54" fill="white" font-size="8" font-weight="bold">MetaLink</text>

    <rect x="25" y="65" width="130" height="5" fill="#e2e8f0" rx="1"/>
    <rect x="25" y="75" width="120" height="5" fill="#e2e8f0" rx="1"/>

    <!-- 服务卡片（垂直排列） -->
    <rect x="25" y="85" width="130" height="25" fill="white" rx="3" filter="url(#shadow)"/>
    <rect x="30" y="90" width="60" height="6" fill="#10b981" rx="1"/>
    <rect x="30" y="100" width="50" height="4" fill="#e2e8f0" rx="1"/>

    <!-- Home按钮 -->
    <circle cx="90" cy="125" r="6" fill="rgba(255,255,255,0.3)"/>

    <!-- 设备标签 -->
    <text x="90" y="150" text-anchor="middle" fill="#1e293b" font-size="11" font-weight="bold">Tablet</text>
    <text x="90" y="165" text-anchor="middle" fill="#64748b" font-size="9">768×1024</text>
  </g>

  <!-- 手机设备 -->
  <g transform="translate(380,420)">
    <!-- 手机外框 -->
    <g filter="url(#shadow)">
      <rect width="90" height="150" fill="url(#mobileGradient)" rx="18"/>
      <rect x="3" y="3" width="84" height="144" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="15"/>
    </g>

    <!-- 屏幕 -->
    <rect x="10" y="20" width="70" height="110" fill="url(#screenGradient)" rx="8" filter="url(#screenGlow)"/>

    <!-- 状态栏 -->
    <rect x="12" y="22" width="66" height="8" fill="#f1f5f9" rx="1"/>
    <text x="15" y="28" fill="#64748b" font-size="6">9:41</text>
    <text x="65" y="28" fill="#64748b" font-size="6">100%</text>

    <!-- 浏览器界面 -->
    <rect x="12" y="35" width="66" height="10" fill="#e2e8f0" rx="1"/>
    <circle cx="17" cy="40" r="1.5" fill="#ef4444"/>
    <circle cx="22" cy="40" r="1.5" fill="#eab308"/>
    <circle cx="27" cy="40" r="1.5" fill="#22c55e"/>

    <!-- 网站内容（手机适配） -->
    <rect x="15" y="50" width="50" height="8" fill="#3b82f6" rx="1"/>
    <text x="17" y="56" fill="white" font-size="6" font-weight="bold">MetaLink</text>

    <rect x="15" y="65" width="60" height="3" fill="#e2e8f0" rx="1"/>
    <rect x="15" y="72" width="55" height="3" fill="#e2e8f0" rx="1"/>
    <rect x="15" y="79" width="50" height="3" fill="#e2e8f0" rx="1"/>

    <!-- 服务按钮（垂直排列） -->
    <rect x="15" y="90" width="60" height="12" fill="white" rx="2" filter="url(#shadow)"/>
    <rect x="18" y="93" width="30" height="3" fill="#10b981" rx="1"/>
    <rect x="18" y="98" width="25" height="2" fill="#e2e8f0" rx="1"/>

    <rect x="15" y="107" width="60" height="12" fill="white" rx="2" filter="url(#shadow)"/>
    <rect x="18" y="110" width="30" height="3" fill="#f59e0b" rx="1"/>
    <rect x="18" y="115" width="25" height="2" fill="#e2e8f0" rx="1"/>

    <!-- Home指示器 -->
    <rect x="35" y="140" width="20" height="3" fill="rgba(255,255,255,0.5)" rx="1.5"/>

    <!-- 设备标签 -->
    <text x="45" y="170" text-anchor="middle" fill="#1e293b" font-size="10" font-weight="bold">Mobile</text>
    <text x="45" y="185" text-anchor="middle" fill="#64748b" font-size="8">375×667</text>
  </g>

  <!-- 响应式指示器 -->
  <g transform="translate(300,100)">
    <circle cx="0" cy="0" r="12" fill="#3b82f6" opacity="0.8">
      <animate attributeName="r" values="12;18;12" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="3s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="4" text-anchor="middle" fill="white" font-size="8" font-weight="bold">RWD</text>
  </g>

  <!-- 标题 -->
  <text x="300" y="60" text-anchor="middle" fill="#1e293b" font-size="28" font-weight="bold" filter="url(#glow)">レスポンシブウェブデザイン</text>
  <text x="300" y="85" text-anchor="middle" fill="#64748b" font-size="14">あらゆるデバイスに最適化されたWebサイト</text>
</svg>
