<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="databaseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3730a3;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="inventoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="salesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="productionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="financeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ea580c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c2410c;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="5" stdDeviation="4" flood-color="#000000" flood-opacity="0.25"/>
    </filter>

    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 数据流动效果 -->
    <filter id="dataFlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="600" height="600" fill="url(#bgGradient)"/>

  <!-- 装饰性网格 -->
  <defs>
    <pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse">
      <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#cbd5e1" stroke-width="0.5" opacity="0.2"/>
    </pattern>
  </defs>
  <rect width="600" height="600" fill="url(#grid)"/>

  <!-- 连接线（放在模块下方） -->
  <g stroke="#64748b" stroke-width="3" opacity="0.7">
    <!-- 库存管理 -->
    <path d="M200,180 Q225,215 250,250" fill="none" stroke-dasharray="8,4" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" repeatCount="indefinite"/>
    </path>
    <!-- 销售管理 -->
    <path d="M450,180 Q375,215 300,250" fill="none" stroke-dasharray="8,4" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" begin="0.5s" repeatCount="indefinite"/>
    </path>
    <!-- 生产管理 -->
    <path d="M200,320 Q225,285 250,250" fill="none" stroke-dasharray="8,4" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" begin="1s" repeatCount="indefinite"/>
    </path>
    <!-- 财务管理 -->
    <path d="M450,320 Q375,285 300,250" fill="none" stroke-dasharray="8,4" filter="url(#dataFlow)">
      <animate attributeName="stroke-dashoffset" values="0;12" dur="2s" begin="1.5s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 中央数据库 -->
  <g transform="translate(300,300)" filter="url(#shadow)">
    <!-- 数据库主体 -->
    <rect x="-60" y="-80" width="120" height="160" fill="url(#databaseGradient)" rx="15"/>
    <rect x="-55" y="-75" width="110" height="150" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>

    <!-- 数据库图标 -->
    <ellipse cx="0" cy="-50" rx="35" ry="8" fill="rgba(255,255,255,0.4)"/>
    <ellipse cx="0" cy="-30" rx="35" ry="8" fill="rgba(255,255,255,0.3)"/>
    <ellipse cx="0" cy="-10" rx="35" ry="8" fill="rgba(255,255,255,0.3)"/>
    <ellipse cx="0" cy="10" rx="35" ry="8" fill="rgba(255,255,255,0.3)"/>

    <!-- 文字 -->
    <text x="0" y="40" text-anchor="middle" fill="white" font-size="16" font-weight="bold">データベース</text>
    <text x="0" y="58" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="11">統合管理</text>
  </g>

  <!-- 模块 -->
  <g>
    <!-- 库存管理 -->
    <g filter="url(#shadow)">
      <rect x="100" y="130" width="120" height="100" fill="url(#inventoryGradient)" rx="15"/>
      <rect x="105" y="135" width="110" height="90" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="160" y="170" text-anchor="middle" fill="white" font-size="14" font-weight="bold">在庫管理</text>
    <text x="160" y="188" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Inventory</text>
    <text x="160" y="205" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="9">📦 商品・資材管理</text>

    <!-- 销售管理 -->
    <g filter="url(#shadow)">
      <rect x="380" y="130" width="120" height="100" fill="url(#salesGradient)" rx="15"/>
      <rect x="385" y="135" width="110" height="90" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="440" y="170" text-anchor="middle" fill="white" font-size="14" font-weight="bold">販売管理</text>
    <text x="440" y="188" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Sales</text>
    <text x="440" y="205" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="9">💰 受注・売上管理</text>

    <!-- 生产管理 -->
    <g filter="url(#shadow)">
      <rect x="100" y="370" width="120" height="100" fill="url(#productionGradient)" rx="15"/>
      <rect x="105" y="375" width="110" height="90" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="160" y="410" text-anchor="middle" fill="white" font-size="14" font-weight="bold">生産管理</text>
    <text x="160" y="428" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Production</text>
    <text x="160" y="445" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="9">🏭 製造・工程管理</text>

    <!-- 财务管理 -->
    <g filter="url(#shadow)">
      <rect x="380" y="370" width="120" height="100" fill="url(#financeGradient)" rx="15"/>
      <rect x="385" y="375" width="110" height="90" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="12"/>
    </g>
    <text x="440" y="410" text-anchor="middle" fill="white" font-size="14" font-weight="bold">財務管理</text>
    <text x="440" y="428" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-size="10">Finance</text>
    <text x="440" y="445" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="9">💳 会計・予算管理</text>
  </g>

  <!-- 数据流动指示器 -->
  <g fill="#3b82f6" opacity="0.6">
    <circle cx="225" cy="200" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="375" cy="200" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" begin="0.75s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="3s" begin="0.75s" repeatCount="indefinite"/>
    </circle>
    <circle cx="225" cy="400" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" begin="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="3s" begin="1.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="375" cy="400" r="4">
      <animate attributeName="r" values="4;8;4" dur="3s" begin="2.25s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="3s" begin="2.25s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- 标题 -->
  <text x="300" y="60" text-anchor="middle" fill="#1e293b" font-size="28" font-weight="bold" filter="url(#glow)">ERPシステム構成</text>
  <text x="300" y="85" text-anchor="middle" fill="#64748b" font-size="14">統合業務管理システム</text>
</svg>
